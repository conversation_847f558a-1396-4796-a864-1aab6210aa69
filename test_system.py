# -*- coding: utf-8 -*-
"""
系统测试脚本
测试所有模块是否正常工作
"""

import warnings
warnings.filterwarnings("ignore")

def test_imports():
    """测试所有模块导入"""
    print("=== 测试模块导入 ===")
    
    modules_to_test = [
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'),
        ('torch', 'PyTorch'),
        ('sklearn', 'Scikit-learn'),
        ('skimage', 'Scikit-image'),
        ('matplotlib', 'Matplotlib'),
        ('ultralytics', 'Ultralytics'),
        ('PyQt5', 'PyQt5')
    ]
    
    success_count = 0
    
    for module_name, display_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {display_name}: 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"❌ {display_name}: 导入失败 - {e}")
    
    print(f"\n导入测试完成: {success_count}/{len(modules_to_test)} 成功")
    return success_count == len(modules_to_test)

def test_algorithm_modules():
    """测试算法模块"""
    print("\n=== 测试算法模块 ===")
    
    algorithm_modules = [
        ('traditional_algorithms', 'TraditionalCrackDetector', '传统算法'),
        ('deep_learning_algorithms', 'DeepLearningCrackDetector', '深度学习算法'),
        ('transformer_models', 'TransformerCrackDetector', 'Transformer算法'),
        ('pixel_calibration', 'PixelCalibrationCalculator', '像素标定'),
        ('comprehensive_crack_detection_system', 'ComprehensiveCrackDetectionSystem', '综合检测系统')
    ]
    
    success_count = 0
    
    for module_name, class_name, display_name in algorithm_modules:
        try:
            module = __import__(module_name)
            cls = getattr(module, class_name)
            print(f"✅ {display_name}: 模块导入成功")
            success_count += 1
        except Exception as e:
            print(f"❌ {display_name}: 模块导入失败 - {e}")
    
    print(f"\n算法模块测试完成: {success_count}/{len(algorithm_modules)} 成功")
    return success_count == len(algorithm_modules)

def test_gui_module():
    """测试GUI模块"""
    print("\n=== 测试GUI模块 ===")
    
    try:
        from algorithm_selection_gui import AlgorithmSelectionGUI
        print("✅ GUI模块: 导入成功")
        return True
    except Exception as e:
        print(f"❌ GUI模块: 导入失败 - {e}")
        return False

def create_test_image():
    """创建测试图像"""
    print("\n=== 创建测试图像 ===")
    
    try:
        import numpy as np
        import cv2
        import os
        
        # 创建测试图像目录
        os.makedirs("test_images", exist_ok=True)
        
        # 创建一个简单的测试图像（模拟裂缝）
        image = np.ones((400, 600, 3), dtype=np.uint8) * 200  # 灰色背景
        
        # 添加一些"裂缝"线条
        cv2.line(image, (50, 100), (550, 300), (50, 50, 50), 3)
        cv2.line(image, (100, 50), (500, 350), (40, 40, 40), 2)
        cv2.line(image, (200, 200), (400, 250), (30, 30, 30), 2)
        
        # 添加一些噪声
        noise = np.random.randint(0, 50, image.shape, dtype=np.uint8)
        image = cv2.subtract(image, noise)
        
        # 保存测试图像
        test_image_path = "test_images/sample_crack.jpg"
        cv2.imwrite(test_image_path, image)
        
        print(f"✅ 测试图像已创建: {test_image_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试图像失败: {e}")
        return False

def test_traditional_algorithm():
    """测试传统算法"""
    print("\n=== 测试传统算法 ===")
    
    try:
        import cv2
        from traditional_algorithms import TraditionalCrackDetector
        
        # 读取测试图像
        image = cv2.imread("test_images/sample_crack.jpg")
        if image is None:
            print("❌ 无法读取测试图像")
            return False
        
        # 创建检测器
        detector = TraditionalCrackDetector(debug_mode=False)
        
        # 测试Otsu方法
        result = detector.comprehensive_detection(image, method='otsu')
        
        if 'error' in result:
            print(f"❌ 传统算法测试失败: {result['error']}")
            return False
        
        print("✅ 传统算法测试成功")
        print(f"   - 检测方法: {result['statistics']['method']}")
        print(f"   - 处理时间: {result['statistics']['processing_time']:.3f}s")
        print(f"   - 裂缝数量: {result['statistics']['crack_count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 传统算法测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔬 混凝土裂缝检测系统 - 完整性测试")
    print("=" * 50)
    
    # 测试基础依赖
    if not test_imports():
        print("\n❌ 基础依赖测试失败，请检查环境配置")
        return False
    
    # 测试算法模块
    if not test_algorithm_modules():
        print("\n❌ 算法模块测试失败")
        return False
    
    # 测试GUI模块
    gui_ok = test_gui_module()
    
    # 创建测试图像
    if not create_test_image():
        print("\n❌ 测试图像创建失败")
        return False
    
    # 测试传统算法
    if not test_traditional_algorithm():
        print("\n❌ 算法功能测试失败")
        return False
    
    # 总结
    print("\n" + "=" * 50)
    print("🎉 系统测试完成！")
    print("\n✅ 所有核心功能正常")
    if gui_ok:
        print("✅ GUI界面可用")
        print("\n🚀 启动GUI: python launch_gui.py")
    else:
        print("⚠️  GUI界面有问题，但核心算法功能正常")
    
    print("🚀 命令行使用: python quick_start.py --image test_images/sample_crack.jpg")
    print("📚 查看文档: ALGORITHM_SYSTEM_README.md")
    
    return True

if __name__ == "__main__":
    main()
