# -*- coding: utf-8 -*-
"""
混凝土裂缝检测专业模块

功能：
1. 图像预处理：对输入的混凝土图像进行去噪、灰度化、二值化等操作
2. 裂缝检测：使用Canny边缘检测算法和Gabor滤波器纹理检测算法
3. 裂缝分割：将检测到的裂缝区域精确分割出来
4. 长度计算：对裂缝区域进行骨架提取，计算骨架长度得到裂缝长度
5. 宽度计算：在裂缝区域上下两侧选取若干个点，计算距离得到平均宽度
6. 面积计算：统计裂缝区域的像素点个数，得到裂缝面积
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from skimage.morphology import skeletonize, thin, medial_axis
from skimage import measure, filters, segmentation
from scipy import ndimage, spatial
from scipy.ndimage import distance_transform_edt
from datetime import datetime
import os
import csv
import json


class ConcreteCrackDetector:
    def __init__(self, scale_factor=1.0, unit='mm'):
        """初始化混凝土裂缝检测器
        :param scale_factor: 比例尺（像素/实际单位）
        :param unit: 长度单位，默认为毫米
        """
        self.scale_factor = scale_factor
        self.unit = unit
        
        # 图像预处理参数
        self.preprocess_params = {
            'gaussian_kernel': (5, 5),    # 高斯滤波核大小
            'gaussian_sigma': 1.0,        # 高斯滤波标准差
            'median_kernel': 5,           # 中值滤波核大小
            'bilateral_d': 9,             # 双边滤波邻域直径
            'bilateral_sigma_color': 75,  # 双边滤波颜色空间标准差
            'bilateral_sigma_space': 75,  # 双边滤波坐标空间标准差
            'clahe_clip_limit': 3.0,      # CLAHE裁剪限制
            'clahe_tile_grid': (8, 8),    # CLAHE网格大小
        }
        
        # Canny边缘检测参数
        self.canny_params = {
            'low_threshold': 30,          # Canny低阈值
            'high_threshold': 100,        # Canny高阈值
            'aperture_size': 3,           # Sobel算子孔径大小
            'l2_gradient': True           # 使用L2梯度
        }
        
        # Gabor滤波器参数
        self.gabor_params = {
            'frequencies': [0.05, 0.15, 0.25, 0.35],  # 频率列表
            'orientations': [0, 30, 60, 90, 120, 150],  # 方向列表（度）
            'sigma_x': 3.0,               # X方向标准差
            'sigma_y': 3.0,               # Y方向标准差
            'bandwidth': 1.0,             # 带宽
        }
        
        # 二值化参数
        self.binarization_params = {
            'method': 'adaptive',         # 二值化方法：'otsu', 'adaptive', 'manual', 'multi_otsu'
            'threshold_value': 127,       # 手动阈值
            'adaptive_method': cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            'adaptive_type': cv2.THRESH_BINARY_INV,
            'block_size': 15,             # 自适应阈值块大小
            'c_constant': 5               # 自适应阈值常数
        }
        
        # 形态学处理参数
        self.morphology_params = {
            'opening_kernel_size': (3, 3),   # 开运算核大小
            'closing_kernel_size': (7, 7),   # 闭运算核大小
            'dilation_kernel_size': (3, 3),  # 膨胀核大小
            'erosion_kernel_size': (2, 2),   # 腐蚀核大小
            'iterations': 1                   # 迭代次数
        }
        
        # 裂缝筛选参数
        self.crack_filter_params = {
            'min_area': 100,              # 最小面积
            'max_area': 50000,            # 最大面积
            'min_length': 20,             # 最小长度
            'min_aspect_ratio': 3.0,      # 最小长宽比
            'max_solidity': 0.8,          # 最大实心度
        }

    def advanced_preprocess(self, image):
        """高级图像预处理
        对输入的混凝土图像进行去噪、灰度化、增强等操作
        :param image: 输入图像
        :return: 预处理后的图像和处理步骤
        """
        steps = {}
        
        # 1. 灰度化
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        steps['1_grayscale'] = gray.copy()
        
        # 2. 高斯去噪
        gaussian_filtered = cv2.GaussianBlur(
            gray, 
            self.preprocess_params['gaussian_kernel'],
            self.preprocess_params['gaussian_sigma']
        )
        steps['2_gaussian_filtered'] = gaussian_filtered.copy()
        
        # 3. 中值滤波去椒盐噪声
        median_filtered = cv2.medianBlur(
            gaussian_filtered, 
            self.preprocess_params['median_kernel']
        )
        steps['3_median_filtered'] = median_filtered.copy()
        
        # 4. 双边滤波保边去噪
        bilateral_filtered = cv2.bilateralFilter(
            median_filtered,
            self.preprocess_params['bilateral_d'],
            self.preprocess_params['bilateral_sigma_color'],
            self.preprocess_params['bilateral_sigma_space']
        )
        steps['4_bilateral_filtered'] = bilateral_filtered.copy()
        
        # 5. CLAHE对比度增强
        clahe = cv2.createCLAHE(
            clipLimit=self.preprocess_params['clahe_clip_limit'], 
            tileGridSize=self.preprocess_params['clahe_tile_grid']
        )
        enhanced = clahe.apply(bilateral_filtered)
        steps['5_clahe_enhanced'] = enhanced.copy()
        
        # 6. 直方图均衡化
        hist_eq = cv2.equalizeHist(enhanced)
        steps['6_histogram_equalized'] = hist_eq.copy()
        
        return hist_eq, steps

    def canny_edge_detection(self, image):
        """Canny边缘检测算法
        :param image: 预处理后的灰度图像
        :return: 边缘检测结果
        """
        edges = cv2.Canny(
            image,
            self.canny_params['low_threshold'],
            self.canny_params['high_threshold'],
            apertureSize=self.canny_params['aperture_size'],
            L2gradient=self.canny_params['l2_gradient']
        )
        return edges

    def gabor_filter_detection(self, image):
        """Gabor滤波器纹理检测算法
        :param image: 预处理后的灰度图像
        :return: Gabor滤波结果和各方向响应
        """
        responses = []
        response_details = {}
        
        for freq in self.gabor_params['frequencies']:
            for angle in self.gabor_params['orientations']:
                # 转换角度为弧度
                theta = np.deg2rad(angle)
                
                # 应用Gabor滤波器
                real, imag = filters.gabor(
                    image, 
                    frequency=freq, 
                    theta=theta,
                    sigma_x=self.gabor_params['sigma_x'],
                    sigma_y=self.gabor_params['sigma_y'],
                    mode='reflect'
                )
                
                # 计算幅值响应
                magnitude = np.sqrt(real**2 + imag**2)
                responses.append(magnitude)
                
                # 保存详细响应
                key = f"freq_{freq:.2f}_angle_{angle}"
                response_details[key] = {
                    'real': real,
                    'imag': imag,
                    'magnitude': magnitude
                }
        
        # 合并所有响应 - 取最大值
        gabor_response = np.maximum.reduce(responses)
        
        # 归一化到0-255
        gabor_response = ((gabor_response - gabor_response.min()) / 
                         (gabor_response.max() - gabor_response.min() + 1e-8) * 255).astype(np.uint8)
        
        return gabor_response, response_details

    def advanced_binarization(self, image):
        """高级二值化处理
        :param image: 输入灰度图像
        :return: 二值化结果和阈值信息
        """
        method = self.binarization_params['method']
        threshold_info = {}
        
        if method == 'otsu':
            # Otsu自动阈值
            threshold_value, binary = cv2.threshold(
                image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU
            )
            threshold_info['otsu_threshold'] = threshold_value
            
        elif method == 'multi_otsu':
            # 多级Otsu阈值
            from skimage.filters import threshold_multiotsu
            thresholds = threshold_multiotsu(image, classes=3)
            binary = np.zeros_like(image)
            binary[image <= thresholds[0]] = 255
            threshold_info['multi_otsu_thresholds'] = thresholds.tolist()
            
        elif method == 'adaptive':
            # 自适应阈值
            binary = cv2.adaptiveThreshold(
                image, 255,
                self.binarization_params['adaptive_method'],
                self.binarization_params['adaptive_type'],
                self.binarization_params['block_size'],
                self.binarization_params['c_constant']
            )
            threshold_info['adaptive_params'] = {
                'block_size': self.binarization_params['block_size'],
                'c_constant': self.binarization_params['c_constant']
            }
            
        else:  # manual
            # 手动阈值
            threshold_value, binary = cv2.threshold(
                image, 
                self.binarization_params['threshold_value'], 
                255, 
                cv2.THRESH_BINARY_INV
            )
            threshold_info['manual_threshold'] = threshold_value
        
        return binary, threshold_info

    def morphological_processing(self, binary_image):
        """形态学处理
        :param binary_image: 二值化图像
        :return: 形态学处理后的图像和处理步骤
        """
        steps = {}
        
        # 开运算去除小噪声
        opening_kernel = cv2.getStructuringElement(
            cv2.MORPH_ELLIPSE, 
            self.morphology_params['opening_kernel_size']
        )
        opened = cv2.morphologyEx(
            binary_image, cv2.MORPH_OPEN, opening_kernel,
            iterations=self.morphology_params['iterations']
        )
        steps['opened'] = opened.copy()
        
        # 闭运算连接断裂的裂缝
        closing_kernel = cv2.getStructuringElement(
            cv2.MORPH_ELLIPSE, 
            self.morphology_params['closing_kernel_size']
        )
        closed = cv2.morphologyEx(
            opened, cv2.MORPH_CLOSE, closing_kernel,
            iterations=self.morphology_params['iterations']
        )
        steps['closed'] = closed.copy()
        
        # 细化操作
        thinned = thin(closed > 0).astype(np.uint8) * 255
        steps['thinned'] = thinned.copy()
        
        # 膨胀恢复裂缝宽度
        dilation_kernel = cv2.getStructuringElement(
            cv2.MORPH_ELLIPSE, 
            self.morphology_params['dilation_kernel_size']
        )
        dilated = cv2.dilate(thinned, dilation_kernel, iterations=2)
        steps['dilated'] = dilated.copy()
        
        return dilated, steps

    def crack_segmentation(self, binary_image):
        """裂缝分割
        将检测到的裂缝区域分割出来，便于后续计算
        :param binary_image: 二值化图像
        :return: 分割后的裂缝区域、轮廓和筛选信息
        """
        # 查找轮廓
        contours, _ = cv2.findContours(
            binary_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )
        
        # 筛选有效裂缝轮廓
        valid_contours = []
        filter_info = []
        
        for i, contour in enumerate(contours):
            # 计算基本几何特征
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)
            
            if area == 0 or perimeter == 0:
                continue
            
            # 计算边界矩形
            rect = cv2.minAreaRect(contour)
            width, height = rect[1]
            aspect_ratio = max(width, height) / (min(width, height) + 1e-8)
            
            # 计算实心度
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            solidity = area / (hull_area + 1e-8)
            
            # 估算长度（使用轮廓周长的一半作为近似）
            estimated_length = perimeter / 2
            
            # 应用筛选条件
            area_valid = (self.crack_filter_params['min_area'] <= area <= 
                         self.crack_filter_params['max_area'])
            length_valid = estimated_length >= self.crack_filter_params['min_length']
            aspect_ratio_valid = aspect_ratio >= self.crack_filter_params['min_aspect_ratio']
            solidity_valid = solidity <= self.crack_filter_params['max_solidity']
            
            is_valid = area_valid and length_valid and aspect_ratio_valid and solidity_valid
            
            filter_info.append({
                'index': i,
                'area': area,
                'perimeter': perimeter,
                'aspect_ratio': aspect_ratio,
                'solidity': solidity,
                'estimated_length': estimated_length,
                'area_valid': area_valid,
                'length_valid': length_valid,
                'aspect_ratio_valid': aspect_ratio_valid,
                'solidity_valid': solidity_valid,
                'is_valid': is_valid
            })
            
            if is_valid:
                valid_contours.append(contour)
        
        # 创建分割掩码
        segmentation_mask = np.zeros_like(binary_image)
        if valid_contours:
            cv2.fillPoly(segmentation_mask, valid_contours, 255)
        
        return segmentation_mask, valid_contours, filter_info

    def calculate_crack_length(self, binary_crack_region):
        """长度计算：对裂缝区域进行骨架提取，计算骨架长度
        :param binary_crack_region: 二值化裂缝区域
        :return: 长度计算结果
        """
        # 骨架提取
        skeleton = skeletonize(binary_crack_region > 0)

        # 计算骨架像素数量（基础长度）
        skeleton_pixels = np.sum(skeleton)

        # 更精确的长度计算 - 考虑对角线连接
        skeleton_coords = np.column_stack(np.where(skeleton))

        if len(skeleton_coords) < 2:
            return {
                'length_pixels': skeleton_pixels,
                'length_actual': skeleton_pixels * self.scale_factor,
                'skeleton': skeleton,
                'skeleton_coords': skeleton_coords,
                'precise_length_pixels': skeleton_pixels,
                'precise_length_actual': skeleton_pixels * self.scale_factor
            }

        # 构建骨架点的连接图
        from scipy.spatial.distance import pdist, squareform

        # 计算所有点之间的距离
        distances = squareform(pdist(skeleton_coords))

        # 找到相邻的骨架点（距离小于sqrt(2)的点）
        adjacent_threshold = np.sqrt(2) + 0.1
        adjacent_mask = (distances > 0) & (distances <= adjacent_threshold)

        # 计算精确长度
        precise_length = 0
        for i in range(len(skeleton_coords)):
            neighbors = np.where(adjacent_mask[i])[0]
            for j in neighbors:
                if j > i:  # 避免重复计算
                    distance = distances[i, j]
                    precise_length += distance

        # 如果没有找到相邻点，使用基础方法
        if precise_length == 0:
            precise_length = skeleton_pixels

        return {
            'length_pixels': skeleton_pixels,
            'length_actual': skeleton_pixels * self.scale_factor,
            'skeleton': skeleton,
            'skeleton_coords': skeleton_coords,
            'precise_length_pixels': precise_length,
            'precise_length_actual': precise_length * self.scale_factor
        }

    def calculate_crack_width(self, binary_crack_region, skeleton_coords, num_sample_points=30):
        """宽度计算：在裂缝区域上下两侧选取若干个点，计算距离得到平均宽度
        :param binary_crack_region: 二值化裂缝区域
        :param skeleton_coords: 骨架坐标点
        :param num_sample_points: 采样点数量
        :return: 宽度计算结果
        """
        if len(skeleton_coords) == 0:
            return {
                'width_measurements': [],
                'avg_width_pixels': 0,
                'avg_width_actual': 0,
                'max_width_pixels': 0,
                'max_width_actual': 0,
                'min_width_pixels': 0,
                'min_width_actual': 0,
                'width_std_pixels': 0,
                'width_std_actual': 0,
                'sample_points': [],
                'width_lines': []
            }

        # 均匀采样骨架点
        if len(skeleton_coords) > num_sample_points:
            indices = np.linspace(0, len(skeleton_coords)-1, num_sample_points, dtype=int)
            sample_points = skeleton_coords[indices]
        else:
            sample_points = skeleton_coords

        width_measurements = []
        width_lines = []  # 存储宽度测量线段

        # 计算距离变换
        distance_transform = distance_transform_edt(binary_crack_region)

        for point in sample_points:
            y, x = point

            # 方法1: 使用距离变换
            # 距离变换值的2倍近似为该点的宽度
            dt_width = distance_transform[y, x] * 2

            # 方法2: 垂直方向搜索边界
            # 向上搜索边界
            upper_bound = y
            for i in range(y, max(0, y-50), -1):
                if i < 0 or i >= binary_crack_region.shape[0]:
                    break
                if binary_crack_region[i, x] == 0:
                    upper_bound = i + 1
                    break

            # 向下搜索边界
            lower_bound = y
            for i in range(y, min(binary_crack_region.shape[0], y+50)):
                if i >= binary_crack_region.shape[0]:
                    break
                if binary_crack_region[i, x] == 0:
                    lower_bound = i - 1
                    break

            # 垂直方向宽度
            vertical_width = abs(lower_bound - upper_bound) + 1

            # 方法3: 水平方向搜索边界
            # 向左搜索边界
            left_bound = x
            for i in range(x, max(0, x-50), -1):
                if i < 0 or i >= binary_crack_region.shape[1]:
                    break
                if binary_crack_region[y, i] == 0:
                    left_bound = i + 1
                    break

            # 向右搜索边界
            right_bound = x
            for i in range(x, min(binary_crack_region.shape[1], x+50)):
                if i >= binary_crack_region.shape[1]:
                    break
                if binary_crack_region[y, i] == 0:
                    right_bound = i - 1
                    break

            # 水平方向宽度
            horizontal_width = abs(right_bound - left_bound) + 1

            # 选择较小的宽度值（更保守的估计）
            final_width = min(dt_width, vertical_width, horizontal_width)

            if final_width > 0:
                width_measurements.append(final_width)

                # 记录宽度测量线段
                if vertical_width <= horizontal_width:
                    # 使用垂直线段
                    width_lines.append([(x, upper_bound), (x, lower_bound)])
                else:
                    # 使用水平线段
                    width_lines.append([(left_bound, y), (right_bound, y)])

        # 计算统计信息
        if width_measurements:
            width_array = np.array(width_measurements)
            avg_width_pixels = np.mean(width_array)
            max_width_pixels = np.max(width_array)
            min_width_pixels = np.min(width_array)
            width_std_pixels = np.std(width_array)

            # 转换为实际单位
            avg_width_actual = avg_width_pixels * self.scale_factor
            max_width_actual = max_width_pixels * self.scale_factor
            min_width_actual = min_width_pixels * self.scale_factor
            width_std_actual = width_std_pixels * self.scale_factor
        else:
            avg_width_pixels = max_width_pixels = min_width_pixels = width_std_pixels = 0
            avg_width_actual = max_width_actual = min_width_actual = width_std_actual = 0

        return {
            'width_measurements': width_measurements,
            'avg_width_pixels': avg_width_pixels,
            'avg_width_actual': avg_width_actual,
            'max_width_pixels': max_width_pixels,
            'max_width_actual': max_width_actual,
            'min_width_pixels': min_width_pixels,
            'min_width_actual': min_width_actual,
            'width_std_pixels': width_std_pixels,
            'width_std_actual': width_std_actual,
            'sample_points': sample_points.tolist(),
            'width_lines': width_lines
        }

    def calculate_crack_area(self, binary_crack_region):
        """面积计算：统计裂缝区域的像素点个数，得到裂缝面积
        :param binary_crack_region: 二值化裂缝区域
        :return: 面积计算结果
        """
        # 统计白色像素（裂缝像素）
        crack_pixels = np.sum(binary_crack_region > 0)

        # 转换为实际面积
        actual_area = crack_pixels * (self.scale_factor ** 2)

        # 计算裂缝区域的几何特征
        if crack_pixels > 0:
            # 找到裂缝区域的轮廓
            contours, _ = cv2.findContours(
                binary_crack_region, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
            )

            total_contour_area = 0
            total_perimeter = 0
            bounding_areas = []

            for contour in contours:
                contour_area = cv2.contourArea(contour)
                perimeter = cv2.arcLength(contour, True)

                total_contour_area += contour_area
                total_perimeter += perimeter

                # 计算边界矩形面积
                x, y, w, h = cv2.boundingRect(contour)
                bounding_area = w * h
                bounding_areas.append(bounding_area)

            # 计算填充率（实际面积/边界矩形面积）
            total_bounding_area = sum(bounding_areas)
            fill_ratio = total_contour_area / (total_bounding_area + 1e-8)

        else:
            total_contour_area = 0
            total_perimeter = 0
            fill_ratio = 0

        return {
            'area_pixels': crack_pixels,
            'area_actual': actual_area,
            'contour_area_pixels': total_contour_area,
            'contour_area_actual': total_contour_area * (self.scale_factor ** 2),
            'total_perimeter_pixels': total_perimeter,
            'total_perimeter_actual': total_perimeter * self.scale_factor,
            'fill_ratio': fill_ratio,
            'unit': self.unit
        }

    def detect_concrete_cracks(self, image, return_debug=False):
        """完整的混凝土裂缝检测流程
        :param image: 输入图像
        :param return_debug: 是否返回调试信息
        :return: 检测结果
        """
        debug_info = {} if return_debug else None

        try:
            # 1. 图像预处理
            preprocessed, preprocess_steps = self.advanced_preprocess(image)
            if return_debug:
                debug_info['preprocess_steps'] = preprocess_steps

            # 2. Canny边缘检测
            canny_edges = self.canny_edge_detection(preprocessed)
            if return_debug:
                debug_info['canny_edges'] = canny_edges

            # 3. Gabor滤波器检测
            gabor_response, gabor_details = self.gabor_filter_detection(preprocessed)
            if return_debug:
                debug_info['gabor_response'] = gabor_response
                debug_info['gabor_details'] = gabor_details

            # 4. 融合检测结果
            # 使用加权融合
            canny_weight = 0.6
            gabor_weight = 0.4
            combined = cv2.addWeighted(canny_edges, canny_weight, gabor_response, gabor_weight, 0)
            if return_debug:
                debug_info['combined_detection'] = combined

            # 5. 二值化
            binary, threshold_info = self.advanced_binarization(combined)
            if return_debug:
                debug_info['binary'] = binary
                debug_info['threshold_info'] = threshold_info

            # 6. 形态学处理
            morphed, morph_steps = self.morphological_processing(binary)
            if return_debug:
                debug_info['morphed'] = morphed
                debug_info['morph_steps'] = morph_steps

            # 7. 裂缝分割
            segmentation_mask, contours, filter_info = self.crack_segmentation(morphed)
            if return_debug:
                debug_info['segmentation_mask'] = segmentation_mask
                debug_info['contours'] = contours
                debug_info['filter_info'] = filter_info

            # 8. 长度计算
            length_result = self.calculate_crack_length(segmentation_mask)
            if return_debug:
                debug_info['length_result'] = length_result

            # 9. 宽度计算
            width_result = self.calculate_crack_width(
                segmentation_mask,
                length_result['skeleton_coords']
            )
            if return_debug:
                debug_info['width_result'] = width_result

            # 10. 面积计算
            area_result = self.calculate_crack_area(segmentation_mask)
            if return_debug:
                debug_info['area_result'] = area_result

            # 整理最终结果
            result = {
                'crack_detected': len(contours) > 0,
                'num_cracks': len(contours),
                'contours': contours,
                'segmentation_mask': segmentation_mask,

                # 长度信息
                'length_pixels': length_result['precise_length_pixels'],
                'length_actual': length_result['precise_length_actual'],
                'skeleton': length_result['skeleton'],
                'skeleton_coords': length_result['skeleton_coords'],

                # 宽度信息
                'avg_width_pixels': width_result['avg_width_pixels'],
                'avg_width_actual': width_result['avg_width_actual'],
                'max_width_pixels': width_result['max_width_pixels'],
                'max_width_actual': width_result['max_width_actual'],
                'min_width_pixels': width_result['min_width_pixels'],
                'min_width_actual': width_result['min_width_actual'],
                'width_std_pixels': width_result['width_std_pixels'],
                'width_std_actual': width_result['width_std_actual'],
                'width_measurements': width_result['width_measurements'],
                'width_lines': width_result['width_lines'],

                # 面积信息
                'area_pixels': area_result['area_pixels'],
                'area_actual': area_result['area_actual'],
                'contour_area_pixels': area_result['contour_area_pixels'],
                'contour_area_actual': area_result['contour_area_actual'],
                'fill_ratio': area_result['fill_ratio'],

                # 其他信息
                'scale_factor': self.scale_factor,
                'unit': self.unit,
                'detection_method': 'Concrete Crack Detection (Canny + Gabor)',
                'filter_info': filter_info
            }

            if return_debug:
                result['debug_info'] = debug_info

            return result

        except Exception as e:
            return {
                'error': f"混凝土裂缝检测过程中出错: {str(e)}",
                'crack_detected': False
            }

    def visualize_results(self, image, result, save_path=None, show_details=True):
        """可视化检测结果
        :param image: 原始图像
        :param result: 检测结果
        :param save_path: 保存路径
        :param show_details: 是否显示详细信息
        :return: 可视化图像
        """
        if 'error' in result:
            print(f"无法可视化: {result['error']}")
            return image.copy()

        vis_image = image.copy()

        if result['crack_detected']:
            # 绘制轮廓
            cv2.drawContours(vis_image, result['contours'], -1, (0, 255, 0), 2)

            # 绘制骨架
            if 'skeleton' in result:
                skeleton_color = cv2.cvtColor(
                    result['skeleton'].astype(np.uint8) * 255,
                    cv2.COLOR_GRAY2BGR
                )
                skeleton_color[:, :, 0] = 0  # 移除蓝色通道
                skeleton_color[:, :, 1] = 0  # 移除绿色通道
                vis_image = cv2.addWeighted(vis_image, 0.8, skeleton_color, 0.2, 0)

            # 绘制宽度测量线
            if show_details and 'width_lines' in result:
                for line in result['width_lines']:
                    if len(line) == 2:
                        pt1, pt2 = line
                        cv2.line(vis_image, tuple(map(int, pt1)), tuple(map(int, pt2)), (255, 0, 255), 1)

            # 添加测量信息
            y_offset = 30
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.6
            thickness = 2

            # 标题
            cv2.putText(vis_image, "混凝土裂缝检测结果", (10, y_offset),
                       font, font_scale, (255, 0, 0), thickness)
            y_offset += 25

            # 基本信息
            cv2.putText(vis_image, f"裂缝数量: {result['num_cracks']}",
                       (10, y_offset), font, font_scale, (0, 0, 255), thickness)
            y_offset += 25

            cv2.putText(vis_image, f"长度: {result['length_actual']:.2f} {self.unit}",
                       (10, y_offset), font, font_scale, (0, 0, 255), thickness)
            y_offset += 25

            cv2.putText(vis_image, f"平均宽度: {result['avg_width_actual']:.2f} {self.unit}",
                       (10, y_offset), font, font_scale, (0, 0, 255), thickness)
            y_offset += 25

            cv2.putText(vis_image, f"面积: {result['area_actual']:.2f} {self.unit}²",
                       (10, y_offset), font, font_scale, (0, 0, 255), thickness)
            y_offset += 25

            # 详细信息
            if show_details:
                cv2.putText(vis_image, f"最大宽度: {result['max_width_actual']:.2f} {self.unit}",
                           (10, y_offset), font, 0.5, (128, 128, 128), 1)
                y_offset += 20

                cv2.putText(vis_image, f"最小宽度: {result['min_width_actual']:.2f} {self.unit}",
                           (10, y_offset), font, 0.5, (128, 128, 128), 1)
                y_offset += 20

                cv2.putText(vis_image, f"填充率: {result['fill_ratio']:.3f}",
                           (10, y_offset), font, 0.5, (128, 128, 128), 1)
        else:
            cv2.putText(vis_image, "未检测到混凝土裂缝", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)

        # 添加时间戳
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        cv2.putText(vis_image, timestamp, (10, vis_image.shape[0] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (128, 128, 128), 1)

        if save_path:
            os.makedirs(os.path.dirname(save_path) if os.path.dirname(save_path) else '.', exist_ok=True)
            cv2.imwrite(save_path, vis_image)

        return vis_image

    def batch_process_images(self, image_paths, output_dir, generate_report=True):
        """批量处理混凝土图像
        :param image_paths: 图像路径列表或文件夹路径
        :param output_dir: 输出目录
        :param generate_report: 是否生成报告
        :return: 处理结果列表
        """
        from pathlib import Path

        # 处理输入路径
        if isinstance(image_paths, str):
            folder_path = Path(image_paths)
            if folder_path.is_dir():
                image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
                image_paths = []
                for ext in image_extensions:
                    image_paths.extend(list(folder_path.glob(f'*{ext}')))
                    image_paths.extend(list(folder_path.glob(f'*{ext.upper()}')))
                image_paths = [str(p) for p in image_paths]
            else:
                image_paths = [image_paths]

        if not image_paths:
            print("未找到图像文件")
            return []

        print(f"开始批量处理 {len(image_paths)} 张混凝土图像...")

        results = []
        successful_count = 0
        failed_count = 0

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        for i, image_path in enumerate(image_paths):
            print(f"处理图像 {i+1}/{len(image_paths)}: {os.path.basename(image_path)}")

            try:
                # 读取图像
                image = cv2.imread(image_path)
                if image is None:
                    result = {
                        'error': f"无法读取图像: {image_path}",
                        'image_path': image_path,
                        'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    results.append(result)
                    failed_count += 1
                    continue

                # 执行检测
                result = self.detect_concrete_cracks(image, return_debug=False)
                result['image_path'] = image_path
                result['timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # 保存可视化结果
                if 'error' not in result:
                    filename = os.path.basename(image_path)
                    base_name = os.path.splitext(filename)[0]
                    vis_path = os.path.join(output_dir, f"{base_name}_concrete_result.jpg")

                    self.visualize_results(image, result, vis_path)
                    result['visualization_path'] = vis_path

                    successful_count += 1
                    print(f"  成功: 检测到 {result['num_cracks']} 个裂缝, "
                          f"总长度 {result['length_actual']:.2f}{self.unit}, "
                          f"平均宽度 {result['avg_width_actual']:.2f}{self.unit}")
                else:
                    failed_count += 1
                    print(f"  失败: {result['error']}")

                results.append(result)

            except Exception as e:
                result = {
                    'error': f"处理过程中出错: {str(e)}",
                    'image_path': image_path,
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                results.append(result)
                failed_count += 1
                print(f"  失败: {str(e)}")

        # 生成报告
        if generate_report:
            report_path = os.path.join(output_dir, f"concrete_crack_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
            self.generate_detailed_report(results, report_path)

        # 打印总结
        print(f"\n混凝土裂缝批量处理完成:")
        print(f"  成功: {successful_count} 张")
        print(f"  失败: {failed_count} 张")
        print(f"  总计: {len(image_paths)} 张")

        return results

    def generate_detailed_report(self, results_list, output_path):
        """生成详细的检测报告
        :param results_list: 检测结果列表
        :param output_path: 输出路径
        """
        try:
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    '图像路径', '检测时间', '是否检测到裂缝', '裂缝数量',
                    '长度(像素)', '长度(实际)', '平均宽度(像素)', '平均宽度(实际)',
                    '最大宽度(像素)', '最大宽度(实际)', '最小宽度(像素)', '最小宽度(实际)',
                    '宽度标准差(像素)', '宽度标准差(实际)', '面积(像素)', '面积(实际)',
                    '轮廓面积(像素)', '轮廓面积(实际)', '填充率', '比例尺', '单位', '检测方法'
                ]

                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for result in results_list:
                    if 'error' in result:
                        writer.writerow({
                            '图像路径': result.get('image_path', ''),
                            '检测时间': result.get('timestamp', ''),
                            '检测方法': '混凝土裂缝专业检测',
                            '是否检测到裂缝': '错误: ' + result['error']
                        })
                        continue

                    writer.writerow({
                        '图像路径': result.get('image_path', ''),
                        '检测时间': result.get('timestamp', datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
                        '是否检测到裂缝': '是' if result['crack_detected'] else '否',
                        '裂缝数量': result['num_cracks'],
                        '长度(像素)': f"{result['length_pixels']:.2f}",
                        '长度(实际)': f"{result['length_actual']:.2f}",
                        '平均宽度(像素)': f"{result['avg_width_pixels']:.2f}",
                        '平均宽度(实际)': f"{result['avg_width_actual']:.2f}",
                        '最大宽度(像素)': f"{result['max_width_pixels']:.2f}",
                        '最大宽度(实际)': f"{result['max_width_actual']:.2f}",
                        '最小宽度(像素)': f"{result['min_width_pixels']:.2f}",
                        '最小宽度(实际)': f"{result['min_width_actual']:.2f}",
                        '宽度标准差(像素)': f"{result['width_std_pixels']:.2f}",
                        '宽度标准差(实际)': f"{result['width_std_actual']:.2f}",
                        '面积(像素)': result['area_pixels'],
                        '面积(实际)': f"{result['area_actual']:.2f}",
                        '轮廓面积(像素)': result['contour_area_pixels'],
                        '轮廓面积(实际)': f"{result['contour_area_actual']:.2f}",
                        '填充率': f"{result['fill_ratio']:.3f}",
                        '比例尺': self.scale_factor,
                        '单位': self.unit,
                        '检测方法': result['detection_method']
                    })

            print(f"详细报告已生成: {output_path}")
            return True

        except Exception as e:
            print(f"报告生成失败: {e}")
            return False

    def set_parameters(self, **kwargs):
        """设置检测参数
        :param kwargs: 参数字典
        """
        for param_group, params in kwargs.items():
            if hasattr(self, param_group):
                param_dict = getattr(self, param_group)
                param_dict.update(params)
                print(f"已更新 {param_group}: {params}")
            else:
                print(f"警告: 未知参数组 {param_group}")

    def get_parameters(self):
        """获取当前参数设置
        :return: 参数字典
        """
        return {
            'scale_factor': self.scale_factor,
            'unit': self.unit,
            'preprocess_params': self.preprocess_params,
            'canny_params': self.canny_params,
            'gabor_params': self.gabor_params,
            'binarization_params': self.binarization_params,
            'morphology_params': self.morphology_params,
            'crack_filter_params': self.crack_filter_params
        }

    def create_debug_visualization(self, image, result, save_dir):
        """创建调试可视化图像
        :param image: 原始图像
        :param result: 检测结果（包含debug_info）
        :param save_dir: 保存目录
        """
        if 'debug_info' not in result:
            print("没有调试信息可显示")
            return

        debug_info = result['debug_info']
        os.makedirs(save_dir, exist_ok=True)

        # 保存预处理步骤
        if 'preprocess_steps' in debug_info:
            for step_name, step_image in debug_info['preprocess_steps'].items():
                save_path = os.path.join(save_dir, f"{step_name}.jpg")
                cv2.imwrite(save_path, step_image)

        # 保存检测步骤
        detection_steps = ['canny_edges', 'gabor_response', 'combined_detection',
                          'binary', 'morphed', 'segmentation_mask']

        for step_name in detection_steps:
            if step_name in debug_info:
                save_path = os.path.join(save_dir, f"{step_name}.jpg")
                cv2.imwrite(save_path, debug_info[step_name])

        # 保存形态学处理步骤
        if 'morph_steps' in debug_info:
            for step_name, step_image in debug_info['morph_steps'].items():
                save_path = os.path.join(save_dir, f"morph_{step_name}.jpg")
                cv2.imwrite(save_path, step_image)

        # 保存骨架
        if 'length_result' in debug_info and 'skeleton' in debug_info['length_result']:
            skeleton_image = debug_info['length_result']['skeleton'].astype(np.uint8) * 255
            save_path = os.path.join(save_dir, "skeleton.jpg")
            cv2.imwrite(save_path, skeleton_image)

        print(f"调试图像已保存到: {save_dir}")


# 示例用法
if __name__ == "__main__":
    print("=== 混凝土裂缝检测专业模块测试 ===")

    # 创建检测器
    detector = ConcreteCrackDetector(scale_factor=0.1, unit='mm')

    # 测试图像路径
    test_image_path = "test_images/sample_crack.jpg"

    if os.path.exists(test_image_path):
        # 读取图像
        image = cv2.imread(test_image_path)

        # 执行检测
        result = detector.detect_concrete_cracks(image, return_debug=True)

        if 'error' not in result:
            print(f"混凝土裂缝检测结果:")
            print(f"  是否检测到裂缝: {result['crack_detected']}")
            print(f"  裂缝数量: {result['num_cracks']}")
            print(f"  长度: {result['length_actual']:.2f} {detector.unit}")
            print(f"  平均宽度: {result['avg_width_actual']:.2f} {detector.unit}")
            print(f"  最大宽度: {result['max_width_actual']:.2f} {detector.unit}")
            print(f"  最小宽度: {result['min_width_actual']:.2f} {detector.unit}")
            print(f"  面积: {result['area_actual']:.2f} {detector.unit}²")
            print(f"  填充率: {result['fill_ratio']:.3f}")

            # 可视化结果
            vis_result = detector.visualize_results(
                image, result,
                save_path="output/concrete_crack_result.jpg"
            )

            # 保存调试图像
            detector.create_debug_visualization(
                image, result,
                save_dir="output/concrete_debug"
            )

            print("混凝土裂缝检测完成！结果已保存。")
        else:
            print(f"检测失败: {result['error']}")
    else:
        print(f"测试图像不存在: {test_image_path}")
        print("请将测试图像放在 test_images/ 目录下")
