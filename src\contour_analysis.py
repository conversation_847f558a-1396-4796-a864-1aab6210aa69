import cv2
import numpy as np

class ContourAnalyzer:
    def __init__(self):
        pass

    def non_max_suppression(self, data, win):
        """非最大值抑制"""
        data_max = cv2.dilate(data, win)
        data_max[data != data_max] = 0
        return data_max

    def orientated_non_max_suppression(self, mag, ang):
        """方向性非最大值抑制"""
        ang_quant = np.round(ang / (np.pi/4)) % 4
        winE = np.array([[0, 0, 0],[1, 1, 1], [0, 0, 0]])
        winSE = np.array([[1, 0, 0], [0, 1, 0], [0, 0, 1]])
        winS = np.array([[0, 1, 0], [0, 1, 0], [0, 1, 0]])
        winSW = np.array([[0, 0, 1], [0, 1, 0], [1, 0, 0]])

        magE = self.non_max_suppression(mag, winE)
        magSE = self.non_max_suppression(mag, winSE)
        magS = self.non_max_suppression(mag, winS)
        magSW = self.non_max_suppression(mag, winSW)

        mag[ang_quant == 0] = magE[ang_quant == 0]
        mag[ang_quant == 1] = magSE[ang_quant == 1]
        mag[ang_quant == 2] = magS[ang_quant == 2]
        mag[ang_quant == 3] = magSW[ang_quant == 3]
        return mag

    def morphological_operations(self, binary_image):
        """形态学操作"""
        kernel = np.ones((3, 3), np.uint8)
        return cv2.morphologyEx(binary_image, cv2.MORPH_CLOSE, kernel)