# -*- coding: utf-8 -*-
"""
传统识别算法模块
基于像素级特征的直接处理，包括阈值分割、K-means聚类、Skele-Marker去噪等算法

功能：
1. Otsu阈值分割算法 - 自动计算最优阈值进行二值化
2. K-means聚类算法 - 将像素按灰度值聚类分离裂缝
3. Skele-Marker去噪算法 - 基于骨架提取的去噪处理
4. 形态学处理 - 腐蚀、膨胀、开闭运算等
5. 连通域分析 - 筛选真实裂缝区域
"""

import cv2
import numpy as np
from sklearn.cluster import KMeans
from skimage import morphology, measure
from skimage.morphology import skeletonize, remove_small_objects
import matplotlib.pyplot as plt
from typing import Tuple, List, Dict, Optional
import time
from datetime import datetime
import os


class TraditionalCrackDetector:
    """传统裂缝检测算法类"""
    
    def __init__(self, debug_mode: bool = False):
        """
        初始化传统检测器
        
        Args:
            debug_mode: 是否启用调试模式，保存中间结果
        """
        self.debug_mode = debug_mode
        self.debug_images = {}
        
    def otsu_threshold(self, image: np.ndarray) -> Tuple[np.ndarray, int]:
        """
        Otsu阈值分割算法
        自动计算最优阈值，将图像分为前景（裂缝）和背景
        
        Args:
            image: 输入灰度图像
            
        Returns:
            binary_image: 二值化图像
            threshold: 计算得到的阈值
        """
        if len(image.shape) == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
        # 计算灰度直方图
        hist = cv2.calcHist([image], [0], None, [256], [0, 256])
        hist = hist.flatten()
        
        # 计算总像素数
        total_pixels = image.shape[0] * image.shape[1]
        
        # 初始化变量
        sum_total = np.sum(np.arange(256) * hist)
        sum_background = 0
        weight_background = 0
        max_variance = 0
        optimal_threshold = 0
        
        # 遍历所有可能的阈值
        for threshold in range(256):
            weight_background += hist[threshold]
            if weight_background == 0:
                continue
                
            weight_foreground = total_pixels - weight_background
            if weight_foreground == 0:
                break
                
            sum_background += threshold * hist[threshold]
            
            mean_background = sum_background / weight_background
            mean_foreground = (sum_total - sum_background) / weight_foreground
            
            # 计算类间方差
            variance_between = weight_background * weight_foreground * \
                             (mean_background - mean_foreground) ** 2
            
            # 更新最优阈值
            if variance_between > max_variance:
                max_variance = variance_between
                optimal_threshold = threshold
        
        # 应用阈值进行二值化
        _, binary_image = cv2.threshold(image, optimal_threshold, 255, cv2.THRESH_BINARY_INV)
        
        if self.debug_mode:
            self.debug_images['otsu_threshold'] = binary_image
            print(f"Otsu最优阈值: {optimal_threshold}")
            
        return binary_image, optimal_threshold
    
    def kmeans_clustering(self, image: np.ndarray, k: int = 2) -> np.ndarray:
        """
        K-means聚类算法
        将像素按灰度值聚类，通常分为2类（裂缝和背景）
        
        Args:
            image: 输入灰度图像
            k: 聚类数量，默认为2
            
        Returns:
            clustered_image: 聚类结果图像
        """
        if len(image.shape) == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
        # 将图像重塑为一维数组
        pixel_values = image.reshape((-1, 1))
        pixel_values = np.float32(pixel_values)
        
        # 执行K-means聚类
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        labels = kmeans.fit_predict(pixel_values)
        centers = kmeans.cluster_centers_
        
        # 将标签重塑回图像形状
        labels = labels.reshape(image.shape)
        
        # 创建聚类结果图像
        clustered_image = np.zeros_like(image)
        for i in range(k):
            clustered_image[labels == i] = int(centers[i])
        
        # 假设裂缝对应较暗的聚类中心
        dark_cluster = np.argmin(centers)
        binary_result = np.zeros_like(image)
        binary_result[labels == dark_cluster] = 255
        
        if self.debug_mode:
            self.debug_images['kmeans_clustering'] = binary_result
            print(f"K-means聚类中心: {centers.flatten()}")
            
        return binary_result
    
    def skele_marker_denoising(self, binary_image: np.ndarray, 
                              min_area: int = 50, 
                              min_skeleton_length: int = 20) -> np.ndarray:
        """
        Skele-Marker去噪算法
        基于连通域面积和骨架长度的双重筛选去噪
        
        Args:
            binary_image: 输入二值图像
            min_area: 最小连通域面积阈值
            min_skeleton_length: 最小骨架长度阈值
            
        Returns:
            denoised_image: 去噪后的图像
        """
        # 确保输入是二值图像
        if len(binary_image.shape) == 3:
            binary_image = cv2.cvtColor(binary_image, cv2.COLOR_BGR2GRAY)
        
        binary_image = (binary_image > 127).astype(np.uint8)
        
        # 步骤1: 基于面积的连通域过滤
        labeled_image = measure.label(binary_image)
        area_filtered = remove_small_objects(labeled_image, min_size=min_area)
        area_filtered = (area_filtered > 0).astype(np.uint8)
        
        if self.debug_mode:
            self.debug_images['area_filtered'] = area_filtered * 255
        
        # 步骤2: 提取骨架
        skeleton = skeletonize(area_filtered)
        
        if self.debug_mode:
            self.debug_images['skeleton'] = skeleton.astype(np.uint8) * 255
        
        # 步骤3: 基于骨架长度的筛选
        skeleton_labeled = measure.label(skeleton)
        valid_skeletons = np.zeros_like(skeleton)
        
        for region in measure.regionprops(skeleton_labeled):
            if region.area >= min_skeleton_length:  # 骨架长度即为区域面积
                coords = region.coords
                for coord in coords:
                    valid_skeletons[coord[0], coord[1]] = 1
        
        if self.debug_mode:
            self.debug_images['valid_skeletons'] = valid_skeletons.astype(np.uint8) * 255
        
        # 步骤4: 形态学重建
        # 使用有效骨架作为标记，原始区域作为掩码进行重建
        reconstructed = self._morphological_reconstruction(
            valid_skeletons.astype(np.uint8), 
            area_filtered
        )
        
        if self.debug_mode:
            self.debug_images['reconstructed'] = reconstructed * 255
            
        return reconstructed * 255
    
    def _morphological_reconstruction(self, marker: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """
        形态学重建
        
        Args:
            marker: 标记图像（骨架）
            mask: 掩码图像（原始区域）
            
        Returns:
            reconstructed: 重建后的图像
        """
        kernel = np.ones((3, 3), np.uint8)
        reconstructed = marker.copy()
        
        while True:
            dilated = cv2.dilate(reconstructed, kernel, iterations=1)
            new_reconstructed = cv2.bitwise_and(dilated, mask)
            
            if np.array_equal(new_reconstructed, reconstructed):
                break
            reconstructed = new_reconstructed
            
        return reconstructed
    
    def morphological_operations(self, binary_image: np.ndarray, 
                               operation: str = 'close',
                               kernel_size: int = 3,
                               iterations: int = 1) -> np.ndarray:
        """
        形态学操作
        
        Args:
            binary_image: 输入二值图像
            operation: 操作类型 ('erode', 'dilate', 'open', 'close')
            kernel_size: 结构元素大小
            iterations: 迭代次数
            
        Returns:
            result: 处理后的图像
        """
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        
        if operation == 'erode':
            result = cv2.erode(binary_image, kernel, iterations=iterations)
        elif operation == 'dilate':
            result = cv2.dilate(binary_image, kernel, iterations=iterations)
        elif operation == 'open':
            result = cv2.morphologyEx(binary_image, cv2.MORPH_OPEN, kernel)
        elif operation == 'close':
            result = cv2.morphologyEx(binary_image, cv2.MORPH_CLOSE, kernel)
        else:
            raise ValueError(f"不支持的形态学操作: {operation}")
            
        if self.debug_mode:
            self.debug_images[f'morph_{operation}'] = result
            
        return result
    
    def connected_component_analysis(self, binary_image: np.ndarray,
                                   min_area: int = 100,
                                   max_area: int = 10000,
                                   min_aspect_ratio: float = 2.0) -> Tuple[np.ndarray, List[Dict]]:
        """
        连通域分析
        筛选符合裂缝特征的连通域
        
        Args:
            binary_image: 输入二值图像
            min_area: 最小面积阈值
            max_area: 最大面积阈值
            min_aspect_ratio: 最小长宽比阈值
            
        Returns:
            filtered_image: 筛选后的图像
            crack_info: 裂缝信息列表
        """
        # 连通域标记
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(
            binary_image, connectivity=8
        )
        
        filtered_image = np.zeros_like(binary_image)
        crack_info = []
        
        # 遍历每个连通域（跳过背景）
        for i in range(1, num_labels):
            area = stats[i, cv2.CC_STAT_AREA]
            width = stats[i, cv2.CC_STAT_WIDTH]
            height = stats[i, cv2.CC_STAT_HEIGHT]
            
            # 计算长宽比
            aspect_ratio = max(width, height) / min(width, height) if min(width, height) > 0 else 0
            
            # 筛选条件
            if (min_area <= area <= max_area and aspect_ratio >= min_aspect_ratio):
                # 保留该连通域
                filtered_image[labels == i] = 255
                
                # 记录裂缝信息
                crack_info.append({
                    'id': i,
                    'area': area,
                    'width': width,
                    'height': height,
                    'aspect_ratio': aspect_ratio,
                    'centroid': centroids[i]
                })
        
        if self.debug_mode:
            self.debug_images['connected_components'] = filtered_image
            print(f"检测到 {len(crack_info)} 个符合条件的裂缝区域")
            
        return filtered_image, crack_info

    def comprehensive_detection(self, image: np.ndarray,
                              method: str = 'otsu',
                              enable_denoising: bool = True,
                              enable_morphology: bool = True) -> Dict:
        """
        综合检测方法
        整合多种传统算法的完整检测流程

        Args:
            image: 输入图像
            method: 检测方法 ('otsu', 'kmeans', 'combined')
            enable_denoising: 是否启用去噪
            enable_morphology: 是否启用形态学处理

        Returns:
            result: 检测结果字典
        """
        start_time = time.time()

        # 预处理
        if len(image.shape) == 3:
            gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray_image = image.copy()

        # 图像增强
        enhanced_image = cv2.equalizeHist(gray_image)
        enhanced_image = cv2.medianBlur(enhanced_image, 5)

        if self.debug_mode:
            self.debug_images['enhanced'] = enhanced_image

        # 选择检测方法
        if method == 'otsu':
            binary_image, threshold = self.otsu_threshold(enhanced_image)
        elif method == 'kmeans':
            binary_image = self.kmeans_clustering(enhanced_image)
            threshold = None
        elif method == 'combined':
            # 组合方法：先Otsu后K-means优化
            otsu_result, threshold = self.otsu_threshold(enhanced_image)
            binary_image = self.kmeans_clustering(enhanced_image)
            # 取两种方法的交集
            binary_image = cv2.bitwise_and(otsu_result, binary_image)
        else:
            raise ValueError(f"不支持的检测方法: {method}")

        # 形态学处理
        if enable_morphology:
            binary_image = self.morphological_operations(binary_image, 'close', 3, 1)
            binary_image = self.morphological_operations(binary_image, 'open', 2, 1)

        # 去噪处理
        if enable_denoising:
            binary_image = self.skele_marker_denoising(binary_image)

        # 连通域分析
        final_result, crack_info = self.connected_component_analysis(binary_image)

        # 计算处理时间
        processing_time = time.time() - start_time

        # 统计信息
        total_crack_area = sum([info['area'] for info in crack_info])
        crack_count = len(crack_info)

        result = {
            'binary_image': final_result,
            'crack_info': crack_info,
            'statistics': {
                'method': method,
                'threshold': threshold,
                'crack_count': crack_count,
                'total_area': total_crack_area,
                'processing_time': processing_time
            },
            'debug_images': self.debug_images if self.debug_mode else {}
        }

        return result

    def save_debug_images(self, output_dir: str = "output/traditional_debug"):
        """
        保存调试图像

        Args:
            output_dir: 输出目录
        """
        if not self.debug_mode or not self.debug_images:
            return

        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for name, image in self.debug_images.items():
            filename = f"{name}_{timestamp}.jpg"
            filepath = os.path.join(output_dir, filename)
            cv2.imwrite(filepath, image)

        print(f"调试图像已保存到: {output_dir}")

    def visualize_results(self, original_image: np.ndarray,
                         result: Dict,
                         save_path: Optional[str] = None) -> np.ndarray:
        """
        可视化检测结果

        Args:
            original_image: 原始图像
            result: 检测结果
            save_path: 保存路径（可选）

        Returns:
            visualization: 可视化图像
        """
        # 创建可视化图像
        if len(original_image.shape) == 3:
            visualization = original_image.copy()
        else:
            visualization = cv2.cvtColor(original_image, cv2.COLOR_GRAY2BGR)

        # 在原图上标注裂缝
        binary_image = result['binary_image']
        contours, _ = cv2.findContours(binary_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 绘制裂缝轮廓
        cv2.drawContours(visualization, contours, -1, (0, 0, 255), 2)

        # 添加统计信息
        stats = result['statistics']
        info_text = [
            f"Method: {stats['method']}",
            f"Cracks: {stats['crack_count']}",
            f"Total Area: {stats['total_area']} pixels",
            f"Time: {stats['processing_time']:.3f}s"
        ]

        y_offset = 30
        for i, text in enumerate(info_text):
            cv2.putText(visualization, text, (10, y_offset + i * 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(visualization, text, (10, y_offset + i * 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)

        # 保存结果
        if save_path:
            cv2.imwrite(save_path, visualization)

        return visualization


def demo_traditional_algorithms():
    """传统算法演示函数"""
    # 创建检测器
    detector = TraditionalCrackDetector(debug_mode=True)

    # 测试图像路径
    test_image_path = "test_images/sample_crack.jpg"

    if not os.path.exists(test_image_path):
        print(f"测试图像不存在: {test_image_path}")
        return

    # 读取测试图像
    image = cv2.imread(test_image_path)
    if image is None:
        print("无法读取测试图像")
        return

    print("=== 传统算法裂缝检测演示 ===")

    # 测试不同方法
    methods = ['otsu', 'kmeans', 'combined']

    for method in methods:
        print(f"\n测试方法: {method}")
        result = detector.comprehensive_detection(image, method=method)

        # 显示统计信息
        stats = result['statistics']
        print(f"检测到裂缝数量: {stats['crack_count']}")
        print(f"总面积: {stats['total_area']} 像素")
        print(f"处理时间: {stats['processing_time']:.3f} 秒")

        # 可视化结果
        visualization = detector.visualize_results(
            image, result,
            save_path=f"output/traditional_{method}_result.jpg"
        )

        # 保存调试图像
        detector.save_debug_images(f"output/traditional_{method}_debug")

        # 清空调试图像缓存
        detector.debug_images.clear()


if __name__ == "__main__":
    demo_traditional_algorithms()
