@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              增强版裂缝检测系统 - Windows安装器               ║
echo ║            Enhanced Crack Detection - Windows Installer      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH环境变量
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 📦 开始安装依赖包...
echo.

echo 🔄 升级pip...
python -m pip install --upgrade pip

echo.
echo 📦 安装核心依赖包...
python -m pip install numpy opencv-python matplotlib scikit-image scikit-learn scipy pandas

echo.
echo 🖼️ 安装GUI界面包...
python -m pip install PyQt5

echo.
echo 🤖 安装YOLO深度学习支持 (可选)...
set /p install_yolo="是否安装YOLO支持? (y/n, 默认y): "
if /i "%install_yolo%"=="" set install_yolo=y
if /i "%install_yolo%"=="y" (
    python -m pip install ultralytics
    echo ✅ YOLO支持已安装
) else (
    echo ⚠️ 跳过YOLO支持安装
)

echo.
echo 🧪 创建测试环境...
if not exist "test_images" mkdir test_images
if not exist "output" mkdir output
if not exist "models" mkdir models

echo.
echo 🔍 验证安装...
python install_dependencies.py

echo.
echo 🎉 安装完成！
echo.
echo 现在您可以运行以下命令开始使用:
echo   python quick_start.py          # 交互式启动
echo   python enhanced_crack_gui.py   # 图形界面
echo   python enhanced_crack_detect.py --help  # 命令行帮助
echo.

pause
