#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版裂缝检测系统 - 依赖包自动安装脚本

这个脚本会自动检测和安装所需的依赖包，并提供详细的安装进度和错误处理。
"""

import subprocess
import sys
import os
import importlib
from pathlib import Path


def print_banner():
    """打印安装横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║              增强版裂缝检测系统 - 依赖安装器                  ║
    ║            Enhanced Crack Detection - Dependency Installer   ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python版本过低，需要Python 3.7或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True


def check_pip():
    """检查pip是否可用"""
    print("🔍 检查pip...")
    
    try:
        import pip
        print("✅ pip已安装")
        return True
    except ImportError:
        print("❌ pip未安装")
        return False


def upgrade_pip():
    """升级pip到最新版本"""
    print("🔄 升级pip...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✅ pip升级成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠️ pip升级失败: {e}")
        return False


def install_package(package_name, display_name=None):
    """安装单个包"""
    if display_name is None:
        display_name = package_name
    
    print(f"📦 安装 {display_name}...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package_name, "--quiet"
        ])
        print(f"✅ {display_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {display_name} 安装失败: {e}")
        return False


def check_package_installed(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False


def install_core_packages():
    """安装核心依赖包"""
    print("\n📦 安装核心依赖包...")
    print("-" * 50)
    
    # 核心包列表 (包名, 导入名, 显示名)
    core_packages = [
        ("numpy", "numpy", "NumPy (数值计算)"),
        ("opencv-python", "cv2", "OpenCV (图像处理)"),
        ("matplotlib", "matplotlib", "Matplotlib (图形绘制)"),
        ("scikit-image", "skimage", "Scikit-Image (图像处理)"),
        ("scikit-learn", "sklearn", "Scikit-Learn (机器学习)"),
        ("scipy", "scipy", "SciPy (科学计算)"),
        ("pandas", "pandas", "Pandas (数据处理)"),
    ]
    
    success_count = 0
    total_count = len(core_packages)
    
    for package_name, import_name, display_name in core_packages:
        if check_package_installed(package_name, import_name):
            print(f"✅ {display_name} 已安装")
            success_count += 1
        else:
            if install_package(package_name, display_name):
                success_count += 1
    
    print(f"\n核心包安装结果: {success_count}/{total_count} 成功")
    return success_count == total_count


def install_gui_packages():
    """安装GUI相关包"""
    print("\n🖼️ 安装GUI界面包...")
    print("-" * 50)
    
    gui_packages = [
        ("PyQt5", "PyQt5", "PyQt5 (GUI界面)"),
    ]
    
    success_count = 0
    total_count = len(gui_packages)
    
    for package_name, import_name, display_name in gui_packages:
        if check_package_installed(package_name, import_name):
            print(f"✅ {display_name} 已安装")
            success_count += 1
        else:
            if install_package(package_name, display_name):
                success_count += 1
    
    print(f"\nGUI包安装结果: {success_count}/{total_count} 成功")
    return success_count == total_count


def install_optional_packages():
    """安装可选包"""
    print("\n🔧 安装可选功能包...")
    print("-" * 50)
    
    # 询问是否安装YOLO支持
    install_yolo = input("是否安装YOLO深度学习支持? (y/n, 默认y): ").strip().lower()
    if install_yolo in ['', 'y', 'yes']:
        print("📦 安装YOLO支持包...")
        if install_package("ultralytics", "Ultralytics YOLO"):
            print("✅ YOLO支持已安装")
        else:
            print("⚠️ YOLO支持安装失败，将使用传统图像处理方法")
    
    # 询问是否安装测试框架
    install_test = input("是否安装测试框架? (y/n, 默认n): ").strip().lower()
    if install_test in ['y', 'yes']:
        install_package("pytest", "Pytest (测试框架)")


def create_test_environment():
    """创建测试环境"""
    print("\n🧪 创建测试环境...")
    print("-" * 50)
    
    # 创建必要的目录
    directories = ["test_images", "output", "models"]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            dir_path.mkdir(exist_ok=True)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"✅ 目录已存在: {directory}")
    
    # 创建简单的测试图像
    try:
        import cv2
        import numpy as np
        
        # 创建测试裂缝图像
        test_image = np.zeros((400, 600, 3), dtype=np.uint8)
        test_image.fill(255)  # 白色背景
        
        # 绘制模拟裂缝
        cv2.line(test_image, (100, 100), (500, 300), (0, 0, 0), 3)
        cv2.line(test_image, (200, 50), (400, 350), (0, 0, 0), 2)
        
        # 保存测试图像
        test_image_path = Path("test_images") / "sample_crack.jpg"
        cv2.imwrite(str(test_image_path), test_image)
        print(f"✅ 创建测试图像: {test_image_path}")
        
    except Exception as e:
        print(f"⚠️ 创建测试图像失败: {e}")


def verify_installation():
    """验证安装结果"""
    print("\n🔍 验证安装结果...")
    print("-" * 50)
    
    # 测试核心功能
    try:
        print("测试核心模块导入...")
        
        import numpy as np
        print("✅ NumPy 导入成功")
        
        import cv2
        print("✅ OpenCV 导入成功")
        
        import matplotlib.pyplot as plt
        print("✅ Matplotlib 导入成功")
        
        import skimage
        print("✅ Scikit-Image 导入成功")
        
        import sklearn
        print("✅ Scikit-Learn 导入成功")
        
        # 测试项目模块
        print("\n测试项目模块...")
        
        from crack_detection import EnhancedCrackDetector
        print("✅ 裂缝检测模块导入成功")
        
        from crack_width_calculator import CrackWidthCalculator
        print("✅ 宽度计算模块导入成功")
        
        # 测试GUI模块
        try:
            from PyQt5.QtWidgets import QApplication
            print("✅ GUI模块导入成功")
        except ImportError:
            print("⚠️ GUI模块导入失败，图形界面不可用")
        
        # 测试YOLO模块
        try:
            from ultralytics import YOLO
            print("✅ YOLO模块导入成功")
        except ImportError:
            print("⚠️ YOLO模块导入失败，将使用传统方法")
        
        print("\n🎉 系统验证完成！")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def main():
    """主安装流程"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        print("\n请升级Python版本后重试")
        return False
    
    # 检查pip
    if not check_pip():
        print("\n请先安装pip")
        return False
    
    # 升级pip
    upgrade_pip()
    
    # 安装核心包
    if not install_core_packages():
        print("\n❌ 核心包安装失败，请检查网络连接或权限")
        return False
    
    # 安装GUI包
    install_gui_packages()
    
    # 安装可选包
    install_optional_packages()
    
    # 创建测试环境
    create_test_environment()
    
    # 验证安装
    if verify_installation():
        print("\n" + "="*60)
        print("🎉 安装完成！")
        print("="*60)
        print("现在您可以运行以下命令开始使用:")
        print("  python quick_start.py          # 交互式启动")
        print("  python enhanced_crack_gui.py   # 图形界面")
        print("  python enhanced_crack_detect.py --help  # 命令行帮助")
        print("="*60)
        return True
    else:
        print("\n❌ 安装验证失败，请检查错误信息")
        return False


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n用户中断安装")
        sys.exit(1)
    except Exception as e:
        print(f"\n安装过程中发生错误: {e}")
        sys.exit(1)
