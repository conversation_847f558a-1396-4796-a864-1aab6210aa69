# -*- coding: utf-8 -*-
"""
Transformer模型模块
基于注意力机制的全局特征学习，适用于复杂背景下的裂缝识别

功能：
1. Vision Transformer (ViT) - 图像分类
2. PCTNet - 像素级裂缝分割
3. 自注意力机制 - 捕捉图像全局依赖关系
4. 多头注意力 - 并行处理不同特征
5. 位置编码 - 保持空间位置信息
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import cv2
from typing import Tuple, List, Dict, Optional
import math
import time
from datetime import datetime


class PatchEmbedding(nn.Module):
    """图像分块嵌入层"""
    
    def __init__(self, img_size: int = 224, patch_size: int = 16, 
                 in_channels: int = 3, embed_dim: int = 768):
        """
        初始化分块嵌入层
        
        Args:
            img_size: 输入图像尺寸
            patch_size: 分块大小
            in_channels: 输入通道数
            embed_dim: 嵌入维度
        """
        super(PatchEmbedding, self).__init__()
        
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2
        
        # 使用卷积实现分块和线性投影
        self.projection = nn.Conv2d(in_channels, embed_dim, 
                                   kernel_size=patch_size, stride=patch_size)
        
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入图像 [B, C, H, W]
            
        Returns:
            patches: 分块嵌入 [B, num_patches, embed_dim]
        """
        B, C, H, W = x.shape
        
        # 分块并投影
        x = self.projection(x)  # [B, embed_dim, H//patch_size, W//patch_size]
        x = x.flatten(2)        # [B, embed_dim, num_patches]
        x = x.transpose(1, 2)   # [B, num_patches, embed_dim]
        
        return x


class MultiHeadSelfAttention(nn.Module):
    """多头自注意力机制"""
    
    def __init__(self, embed_dim: int = 768, num_heads: int = 12, dropout: float = 0.1):
        """
        初始化多头自注意力
        
        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            dropout: dropout概率
        """
        super(MultiHeadSelfAttention, self).__init__()
        
        assert embed_dim % num_heads == 0
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.scale = self.head_dim ** -0.5
        
        # Q, K, V线性变换
        self.qkv = nn.Linear(embed_dim, embed_dim * 3)
        self.proj = nn.Linear(embed_dim, embed_dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入特征 [B, N, embed_dim]
            
        Returns:
            output: 注意力输出 [B, N, embed_dim]
        """
        B, N, C = x.shape
        
        # 计算Q, K, V
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)  # [3, B, num_heads, N, head_dim]
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        # 计算注意力分数
        attn = (q @ k.transpose(-2, -1)) * self.scale  # [B, num_heads, N, N]
        attn = F.softmax(attn, dim=-1)
        attn = self.dropout(attn)
        
        # 应用注意力权重
        x = (attn @ v).transpose(1, 2).reshape(B, N, C)  # [B, N, embed_dim]
        x = self.proj(x)
        x = self.dropout(x)
        
        return x


class TransformerBlock(nn.Module):
    """Transformer编码器块"""
    
    def __init__(self, embed_dim: int = 768, num_heads: int = 12, 
                 mlp_ratio: float = 4.0, dropout: float = 0.1):
        """
        初始化Transformer块
        
        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            mlp_ratio: MLP隐藏层维度比例
            dropout: dropout概率
        """
        super(TransformerBlock, self).__init__()
        
        self.norm1 = nn.LayerNorm(embed_dim)
        self.attn = MultiHeadSelfAttention(embed_dim, num_heads, dropout)
        
        self.norm2 = nn.LayerNorm(embed_dim)
        mlp_hidden_dim = int(embed_dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(embed_dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(mlp_hidden_dim, embed_dim),
            nn.Dropout(dropout)
        )
        
    def forward(self, x):
        """前向传播"""
        # 自注意力 + 残差连接
        x = x + self.attn(self.norm1(x))
        
        # MLP + 残差连接
        x = x + self.mlp(self.norm2(x))
        
        return x


class VisionTransformer(nn.Module):
    """Vision Transformer用于图像分类"""
    
    def __init__(self, img_size: int = 224, patch_size: int = 16, 
                 in_channels: int = 3, num_classes: int = 2,
                 embed_dim: int = 768, depth: int = 12, 
                 num_heads: int = 12, mlp_ratio: float = 4.0,
                 dropout: float = 0.1):
        """
        初始化Vision Transformer
        
        Args:
            img_size: 输入图像尺寸
            patch_size: 分块大小
            in_channels: 输入通道数
            num_classes: 分类数量
            embed_dim: 嵌入维度
            depth: Transformer层数
            num_heads: 注意力头数
            mlp_ratio: MLP比例
            dropout: dropout概率
        """
        super(VisionTransformer, self).__init__()
        
        self.num_classes = num_classes
        self.embed_dim = embed_dim
        
        # 分块嵌入
        self.patch_embed = PatchEmbedding(img_size, patch_size, in_channels, embed_dim)
        num_patches = self.patch_embed.num_patches
        
        # 类别token和位置编码
        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        self.pos_embed = nn.Parameter(torch.zeros(1, num_patches + 1, embed_dim))
        self.dropout = nn.Dropout(dropout)
        
        # Transformer编码器
        self.blocks = nn.ModuleList([
            TransformerBlock(embed_dim, num_heads, mlp_ratio, dropout)
            for _ in range(depth)
        ])
        
        # 分类头
        self.norm = nn.LayerNorm(embed_dim)
        self.head = nn.Linear(embed_dim, num_classes)
        
        # 初始化权重
        self._init_weights()
        
    def _init_weights(self):
        """初始化权重"""
        nn.init.trunc_normal_(self.pos_embed, std=0.02)
        nn.init.trunc_normal_(self.cls_token, std=0.02)
        
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def forward(self, x):
        """前向传播"""
        B = x.shape[0]
        
        # 分块嵌入
        x = self.patch_embed(x)  # [B, num_patches, embed_dim]
        
        # 添加类别token
        cls_tokens = self.cls_token.expand(B, -1, -1)
        x = torch.cat((cls_tokens, x), dim=1)  # [B, num_patches+1, embed_dim]
        
        # 添加位置编码
        x = x + self.pos_embed
        x = self.dropout(x)
        
        # Transformer编码器
        for block in self.blocks:
            x = block(x)
        
        x = self.norm(x)
        
        # 分类
        cls_token_final = x[:, 0]  # 取类别token
        output = self.head(cls_token_final)
        
        return output


class ConvolutionalFeedForward(nn.Module):
    """卷积前馈网络 (CFNN)"""
    
    def __init__(self, embed_dim: int = 768, mlp_ratio: float = 4.0, dropout: float = 0.1):
        """
        初始化卷积前馈网络
        
        Args:
            embed_dim: 嵌入维度
            mlp_ratio: 隐藏层维度比例
            dropout: dropout概率
        """
        super(ConvolutionalFeedForward, self).__init__()
        
        hidden_dim = int(embed_dim * mlp_ratio)
        
        self.conv1 = nn.Conv2d(embed_dim, hidden_dim, 3, padding=1, groups=embed_dim)
        self.conv2 = nn.Conv2d(hidden_dim, embed_dim, 3, padding=1, groups=hidden_dim)
        self.norm1 = nn.BatchNorm2d(hidden_dim)
        self.norm2 = nn.BatchNorm2d(embed_dim)
        self.dropout = nn.Dropout2d(dropout)
        
    def forward(self, x):
        """前向传播"""
        # x: [B, embed_dim, H, W]
        x = self.conv1(x)
        x = self.norm1(x)
        x = F.gelu(x)
        x = self.dropout(x)
        
        x = self.conv2(x)
        x = self.norm2(x)
        x = self.dropout(x)
        
        return x


class PCTNet(nn.Module):
    """PCTNet: 结合CNN和Transformer的像素级裂缝分割网络"""
    
    def __init__(self, in_channels: int = 3, out_channels: int = 1,
                 embed_dim: int = 256, num_heads: int = 8, depth: int = 6):
        """
        初始化PCTNet
        
        Args:
            in_channels: 输入通道数
            out_channels: 输出通道数
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            depth: Transformer层数
        """
        super(PCTNet, self).__init__()
        
        # CNN特征提取器
        self.cnn_encoder = nn.Sequential(
            nn.Conv2d(in_channels, 64, 3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, embed_dim, 3, padding=1),
            nn.BatchNorm2d(embed_dim),
            nn.ReLU(inplace=True)
        )
        
        # Transformer编码器（修改为处理2D特征图）
        self.transformer_blocks = nn.ModuleList([
            self._make_transformer_block(embed_dim, num_heads)
            for _ in range(depth)
        ])
        
        # 解码器
        self.decoder = nn.Sequential(
            nn.Conv2d(embed_dim, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 64, 3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, out_channels, 1),
            nn.Sigmoid()
        )
        
    def _make_transformer_block(self, embed_dim: int, num_heads: int):
        """创建适用于2D特征图的Transformer块"""
        return nn.Sequential(
            nn.LayerNorm(embed_dim),
            # 这里简化处理，实际应该实现2D注意力
            nn.Conv2d(embed_dim, embed_dim, 3, padding=1, groups=embed_dim),
            nn.BatchNorm2d(embed_dim),
            nn.ReLU(inplace=True),
            ConvolutionalFeedForward(embed_dim)
        )
    
    def forward(self, x):
        """前向传播"""
        # CNN特征提取
        features = self.cnn_encoder(x)
        
        # Transformer处理
        for block in self.transformer_blocks:
            residual = features
            features = block(features) + residual
        
        # 解码输出
        output = self.decoder(features)
        
        return output


class TransformerCrackDetector:
    """基于Transformer的裂缝检测器"""
    
    def __init__(self, device: str = 'auto'):
        """
        初始化Transformer检测器
        
        Args:
            device: 计算设备
        """
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
            
        print(f"使用设备: {self.device}")
        
        self.vit_model = None
        self.pctnet_model = None
        
    def load_vit_model(self, model_path: Optional[str] = None, 
                      img_size: int = 224, num_classes: int = 2) -> bool:
        """
        加载Vision Transformer模型
        
        Args:
            model_path: 模型权重路径
            img_size: 输入图像尺寸
            num_classes: 分类数量
            
        Returns:
            success: 是否加载成功
        """
        try:
            self.vit_model = VisionTransformer(
                img_size=img_size,
                num_classes=num_classes,
                embed_dim=768,
                depth=12,
                num_heads=12
            )
            
            if model_path and os.path.exists(model_path):
                checkpoint = torch.load(model_path, map_location=self.device)
                self.vit_model.load_state_dict(checkpoint)
                print(f"已加载ViT模型: {model_path}")
            else:
                print("使用未训练的ViT模型（仅用于演示）")
                
            self.vit_model.to(self.device)
            self.vit_model.eval()
            return True
            
        except Exception as e:
            print(f"加载ViT模型失败: {e}")
            return False
    
    def load_pctnet_model(self, model_path: Optional[str] = None) -> bool:
        """
        加载PCTNet模型
        
        Args:
            model_path: 模型权重路径
            
        Returns:
            success: 是否加载成功
        """
        try:
            self.pctnet_model = PCTNet(
                in_channels=3,
                out_channels=1,
                embed_dim=256,
                num_heads=8,
                depth=6
            )
            
            if model_path and os.path.exists(model_path):
                checkpoint = torch.load(model_path, map_location=self.device)
                self.pctnet_model.load_state_dict(checkpoint)
                print(f"已加载PCTNet模型: {model_path}")
            else:
                print("使用未训练的PCTNet模型（仅用于演示）")
                
            self.pctnet_model.to(self.device)
            self.pctnet_model.eval()
            return True
            
        except Exception as e:
            print(f"加载PCTNet模型失败: {e}")
            return False

    def classify_with_vit(self, image: np.ndarray,
                         patch_size: int = 224,
                         stride: int = 112,
                         confidence_threshold: float = 0.5) -> Dict:
        """
        使用Vision Transformer进行图像分类

        Args:
            image: 输入图像
            patch_size: 图像块大小
            stride: 滑动步长
            confidence_threshold: 置信度阈值

        Returns:
            result: 分类结果
        """
        if self.vit_model is None:
            if not self.load_vit_model():
                return {'error': 'ViT模型加载失败'}

        start_time = time.time()

        # 预处理
        if len(image.shape) == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)

        h, w = image_rgb.shape[:2]
        crack_patches = []
        confidence_map = np.zeros((h, w), dtype=np.float32)

        # 滑动窗口处理
        for y in range(0, h - patch_size + 1, stride):
            for x in range(0, w - patch_size + 1, stride):
                patch = image_rgb[y:y+patch_size, x:x+patch_size]

                # 转换为tensor
                patch_tensor = torch.from_numpy(patch).permute(2, 0, 1).float() / 255.0
                patch_tensor = patch_tensor.unsqueeze(0).to(self.device)

                # 调整尺寸到224x224
                if patch_size != 224:
                    patch_tensor = F.interpolate(patch_tensor, size=(224, 224), mode='bilinear')

                # 标准化
                patch_tensor = (patch_tensor - torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1).to(self.device)) / \
                              torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1).to(self.device)

                # 推理
                with torch.no_grad():
                    output = self.vit_model(patch_tensor)
                    probabilities = F.softmax(output, dim=1)
                    crack_prob = probabilities[0, 1].item()  # 裂缝类别概率

                # 更新置信度图
                confidence_map[y:y+patch_size, x:x+patch_size] = np.maximum(
                    confidence_map[y:y+patch_size, x:x+patch_size], crack_prob
                )

                # 记录高置信度的裂缝块
                if crack_prob > confidence_threshold:
                    crack_patches.append({
                        'bbox': (x, y, patch_size, patch_size),
                        'confidence': crack_prob
                    })

        # 生成二值掩码
        binary_mask = (confidence_map > confidence_threshold).astype(np.uint8) * 255

        processing_time = time.time() - start_time

        result = {
            'method': 'vision_transformer',
            'binary_mask': binary_mask,
            'confidence_map': confidence_map,
            'crack_patches': crack_patches,
            'statistics': {
                'patch_count': len(crack_patches),
                'processing_time': processing_time,
                'confidence_threshold': confidence_threshold,
                'model_type': 'ViT'
            }
        }

        return result

    def segment_with_pctnet(self, image: np.ndarray,
                           threshold: float = 0.5) -> Dict:
        """
        使用PCTNet进行像素级分割

        Args:
            image: 输入图像
            threshold: 分割阈值

        Returns:
            result: 分割结果
        """
        if self.pctnet_model is None:
            if not self.load_pctnet_model():
                return {'error': 'PCTNet模型加载失败'}

        start_time = time.time()

        # 预处理
        if len(image.shape) == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)

        original_size = image_rgb.shape[:2]

        # 调整尺寸（PCTNet通常使用256x256或512x512）
        target_size = 256
        image_resized = cv2.resize(image_rgb, (target_size, target_size))

        # 转换为tensor
        input_tensor = torch.from_numpy(image_resized).permute(2, 0, 1).float() / 255.0
        input_tensor = input_tensor.unsqueeze(0).to(self.device)

        # 推理
        with torch.no_grad():
            output = self.pctnet_model(input_tensor)
            probability_map = output.squeeze().cpu().numpy()

        # 调整回原始尺寸
        probability_map = cv2.resize(probability_map, (original_size[1], original_size[0]))

        # 生成二值掩码
        binary_mask = (probability_map > threshold).astype(np.uint8) * 255

        processing_time = time.time() - start_time

        result = {
            'method': 'pctnet_segmentation',
            'binary_mask': binary_mask,
            'probability_map': probability_map,
            'statistics': {
                'processing_time': processing_time,
                'threshold': threshold,
                'crack_pixel_count': np.sum(binary_mask > 0),
                'model_type': 'PCTNet'
            }
        }

        return result

    def comprehensive_detection(self, image: np.ndarray,
                              method: str = 'pctnet',
                              **kwargs) -> Dict:
        """
        综合检测方法

        Args:
            image: 输入图像
            method: 检测方法 ('vit', 'pctnet', 'ensemble')
            **kwargs: 其他参数

        Returns:
            result: 检测结果
        """
        if method == 'vit':
            return self.classify_with_vit(image, **kwargs)
        elif method == 'pctnet':
            return self.segment_with_pctnet(image, **kwargs)
        elif method == 'ensemble':
            return self._ensemble_transformer_detection(image, **kwargs)
        else:
            return {'error': f'不支持的检测方法: {method}'}

    def _ensemble_transformer_detection(self, image: np.ndarray, **kwargs) -> Dict:
        """
        集成Transformer检测方法

        Args:
            image: 输入图像
            **kwargs: 其他参数

        Returns:
            result: 集成检测结果
        """
        start_time = time.time()

        results = {}
        masks = []

        # 运行所有可用的Transformer方法
        methods = ['vit', 'pctnet']

        for method in methods:
            try:
                # 为每种方法准备合适的参数
                method_kwargs = {}
                if method == 'vit':
                    method_kwargs = {
                        'patch_size': kwargs.get('patch_size', 224),
                        'stride': kwargs.get('stride', 112),
                        'confidence_threshold': kwargs.get('confidence_threshold', 0.5)
                    }
                elif method == 'pctnet':
                    method_kwargs = {
                        'threshold': kwargs.get('threshold', 0.5)
                    }

                result = self.comprehensive_detection(image, method=method, **method_kwargs)
                if 'error' not in result:
                    results[method] = result
                    if 'binary_mask' in result:
                        masks.append(result['binary_mask'])
            except Exception as e:
                print(f"{method} 检测失败: {e}")

        if not masks:
            return {'error': '所有Transformer方法都失败了'}

        # 加权融合（PCTNet权重更高，因为它是专门的分割网络）
        weights = {'vit': 0.3, 'pctnet': 0.7}
        ensemble_mask = np.zeros_like(masks[0], dtype=np.float32)

        for method, result in results.items():
            if 'binary_mask' in result:
                weight = weights.get(method, 1.0 / len(results))
                ensemble_mask += (result['binary_mask'] > 0).astype(np.float32) * weight

        # 阈值化
        threshold = 0.5
        final_mask = (ensemble_mask >= threshold).astype(np.uint8) * 255

        processing_time = time.time() - start_time

        result = {
            'method': 'transformer_ensemble',
            'binary_mask': final_mask,
            'ensemble_weights': weights,
            'individual_results': results,
            'statistics': {
                'methods_used': list(results.keys()),
                'processing_time': processing_time,
                'ensemble_threshold': threshold
            }
        }

        return result


def demo_transformer_models():
    """Transformer模型演示函数"""
    print("=== Transformer模型裂缝检测演示 ===")

    # 创建检测器
    detector = TransformerCrackDetector()

    # 测试图像路径
    test_image_path = "test_images/sample_crack.jpg"

    if not os.path.exists(test_image_path):
        print(f"测试图像不存在: {test_image_path}")
        return

    # 读取测试图像
    image = cv2.imread(test_image_path)
    if image is None:
        print("无法读取测试图像")
        return

    # 测试不同方法
    methods = ['vit', 'pctnet', 'ensemble']

    for method in methods:
        print(f"\n测试方法: {method}")

        try:
            result = detector.comprehensive_detection(image, method=method)

            if 'error' in result:
                print(f"检测失败: {result['error']}")
                continue

            # 显示统计信息
            stats = result['statistics']
            print(f"处理时间: {stats['processing_time']:.3f} 秒")
            print(f"模型类型: {stats.get('model_type', 'Ensemble')}")

            if 'patch_count' in stats:
                print(f"裂缝块数量: {stats['patch_count']}")
            elif 'crack_pixel_count' in stats:
                print(f"裂缝像素数: {stats['crack_pixel_count']}")

            # 保存结果
            cv2.imwrite(f"output/transformer_{method}_result.jpg", result['binary_mask'])
            print(f"结果已保存到: output/transformer_{method}_result.jpg")

        except Exception as e:
            print(f"{method} 检测异常: {e}")


if __name__ == "__main__":
    import os
    demo_transformer_models()
