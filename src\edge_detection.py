import cv2
import numpy as np

class EdgeDetector:
    def __init__(self):
        pass

    def sobel_edge_detection(self, image):
        """Sobel边缘检测"""
        sobelx = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=3)
        mag = np.hypot(sobelx, sobely)
        ang = np.arctan2(sobely, sobelx)
        return mag, ang

    def thresholding(self, mag, fudgefactor):
        """阈值处理"""
        threshold = 4 * fudgefactor * np.mean(mag)
        mag[mag < threshold] = 0
        return mag