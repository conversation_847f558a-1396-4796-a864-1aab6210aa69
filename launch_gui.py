# -*- coding: utf-8 -*-
"""
简化的GUI启动脚本
避免复杂的导入依赖，直接启动GUI界面
"""

import sys
import os
import warnings

# 忽略NumPy兼容性警告
warnings.filterwarnings("ignore", message=".*NumPy.*")
warnings.filterwarnings("ignore", category=UserWarning)

def main():
    """启动GUI界面"""
    try:
        print("正在启动混凝土裂缝检测系统GUI...")
        
        # 设置环境变量来避免一些兼容性问题
        os.environ['PYTHONWARNINGS'] = 'ignore'
        
        # 导入GUI模块
        from algorithm_selection_gui import AlgorithmSelectionGUI
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 设置应用程序信息
        app.setApplicationName("混凝土裂缝检测算法选择系统")
        app.setApplicationVersion("1.0")
        app.setOrganizationName("裂缝检测实验室")
        
        # 创建主窗口
        window = AlgorithmSelectionGUI()
        window.show()
        
        print("GUI界面已启动！")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("\n可能的解决方案:")
        print("1. 检查PyQt5是否正确安装: pip install PyQt5")
        print("2. 检查NumPy版本: pip install 'numpy<2.0'")
        print("3. 重新安装scikit-learn: pip install --upgrade scikit-learn")
        
    except Exception as e:
        print(f"启动GUI失败: {e}")
        print("\n请尝试以下解决方案:")
        print("1. 重新激活conda环境")
        print("2. 检查所有依赖包是否正确安装")
        print("3. 使用命令行模式: python quick_start.py --check")

if __name__ == "__main__":
    main()
