# -*- coding: utf-8 -*-
"""
算法选择GUI界面
提供用户友好的界面来选择和配置不同的裂缝检测算法

功能：
1. 算法选择 - 传统算法、深度学习、Transformer
2. 参数配置 - 各算法的参数调整
3. 实时预览 - 显示检测结果
4. 性能对比 - 多算法结果对比
5. 结果导出 - 保存检测结果和报告
"""

import sys
import os
import cv2
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QLabel, QPushButton, 
                            QComboBox, QSpinBox, QDoubleSpinBox, QSlider,
                            QTextEdit, QProgressBar, QTabWidget, QGroupBox,
                            QFileDialog, QMessageBox, QSplitter, QCheckBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QPixmap, QImage, QFont, QPalette, QColor
import time
from datetime import datetime
import json

# 导入算法模块
from traditional_algorithms import TraditionalCrackDetector
from deep_learning_algorithms import DeepLearningCrackDetector
from transformer_models import TransformerCrackDetector
from pixel_calibration import PixelCalibrationCalculator, CameraCalibrator


class DetectionWorker(QThread):
    """检测工作线程"""
    
    finished = pyqtSignal(dict)
    progress = pyqtSignal(int)
    error = pyqtSignal(str)
    
    def __init__(self, image, algorithm_type, method, parameters):
        super().__init__()
        self.image = image
        self.algorithm_type = algorithm_type
        self.method = method
        self.parameters = parameters
        
    def run(self):
        """执行检测"""
        try:
            self.progress.emit(10)
            
            if self.algorithm_type == 'traditional':
                detector = TraditionalCrackDetector(debug_mode=True)
                result = detector.comprehensive_detection(
                    self.image, 
                    method=self.method,
                    **self.parameters
                )
            elif self.algorithm_type == 'deep_learning':
                detector = DeepLearningCrackDetector()
                result = detector.comprehensive_detection(
                    self.image,
                    method=self.method,
                    **self.parameters
                )
            elif self.algorithm_type == 'transformer':
                detector = TransformerCrackDetector()
                result = detector.comprehensive_detection(
                    self.image,
                    method=self.method,
                    **self.parameters
                )
            else:
                raise ValueError(f"不支持的算法类型: {self.algorithm_type}")
            
            self.progress.emit(100)
            self.finished.emit(result)
            
        except Exception as e:
            self.error.emit(str(e))


class AlgorithmSelectionGUI(QMainWindow):
    """算法选择主界面"""
    
    def __init__(self):
        super().__init__()
        self.current_image = None
        self.current_result = None
        self.detection_worker = None
        self.current_algorithm_type = 'traditional'  # 初始化算法类型
        self.param_widgets = {}  # 初始化参数控件字典
        
        # 算法配置
        self.algorithm_configs = {
            'traditional': {
                'otsu': {'enable_denoising': True, 'enable_morphology': True},
                'kmeans': {'enable_denoising': True, 'enable_morphology': True},
                'combined': {'enable_denoising': True, 'enable_morphology': True}
            },
            'deep_learning': {
                'classification': {'patch_size': 224, 'stride': 112, 'confidence_threshold': 0.5},
                'yolo': {'confidence_threshold': 0.5, 'iou_threshold': 0.45},
                'unet': {'threshold': 0.5},
                'ensemble': {}
            },
            'transformer': {
                'vit': {'patch_size': 224, 'stride': 112, 'confidence_threshold': 0.5},
                'pctnet': {'threshold': 0.5},
                'ensemble': {}
            }
        }
        
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("混凝土裂缝检测算法选择系统")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                font-size: 14px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        control_panel = self.create_control_panel()
        splitter.addWidget(control_panel)
        
        # 右侧显示区域
        display_area = self.create_display_area()
        splitter.addWidget(display_area)
        
        # 设置分割器比例
        splitter.setSizes([400, 1000])
        
        # 状态栏
        self.statusBar().showMessage("就绪")
        
    def create_control_panel(self):
        """创建控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 图像加载区域
        image_group = QGroupBox("图像加载")
        image_layout = QVBoxLayout(image_group)
        
        self.load_image_btn = QPushButton("加载图像")
        self.load_image_btn.clicked.connect(self.load_image)
        image_layout.addWidget(self.load_image_btn)
        
        self.image_info_label = QLabel("未加载图像")
        image_layout.addWidget(self.image_info_label)
        
        layout.addWidget(image_group)
        
        # 算法选择区域
        algorithm_group = QGroupBox("算法选择")
        algorithm_layout = QVBoxLayout(algorithm_group)
        
        # 算法类型选择
        self.algorithm_type_combo = QComboBox()
        self.algorithm_type_combo.addItems(["传统算法", "深度学习", "Transformer"])
        self.algorithm_type_combo.currentTextChanged.connect(self.on_algorithm_type_changed)
        algorithm_layout.addWidget(QLabel("算法类型:"))
        algorithm_layout.addWidget(self.algorithm_type_combo)
        
        # 具体方法选择
        self.method_combo = QComboBox()
        self.method_combo.currentTextChanged.connect(self.on_method_changed)
        algorithm_layout.addWidget(QLabel("检测方法:"))
        algorithm_layout.addWidget(self.method_combo)
        
        layout.addWidget(algorithm_group)
        
        # 参数配置区域
        self.params_group = QGroupBox("参数配置")
        self.params_layout = QVBoxLayout(self.params_group)
        layout.addWidget(self.params_group)
        
        # 像素标定区域
        calibration_group = QGroupBox("像素标定")
        calibration_layout = QVBoxLayout(calibration_group)
        
        self.pixel_scale_spinbox = QDoubleSpinBox()
        self.pixel_scale_spinbox.setRange(0.001, 10.0)
        self.pixel_scale_spinbox.setValue(0.1)
        self.pixel_scale_spinbox.setDecimals(6)
        self.pixel_scale_spinbox.setSuffix(" mm/pixel")
        calibration_layout.addWidget(QLabel("像素比例:"))
        calibration_layout.addWidget(self.pixel_scale_spinbox)
        
        layout.addWidget(calibration_group)
        
        # 检测按钮
        self.detect_btn = QPushButton("开始检测")
        self.detect_btn.clicked.connect(self.start_detection)
        self.detect_btn.setEnabled(False)
        layout.addWidget(self.detect_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)
        
        # 结果导出
        export_group = QGroupBox("结果导出")
        export_layout = QVBoxLayout(export_group)
        
        self.save_result_btn = QPushButton("保存结果图像")
        self.save_result_btn.clicked.connect(self.save_result_image)
        self.save_result_btn.setEnabled(False)
        export_layout.addWidget(self.save_result_btn)
        
        self.save_report_btn = QPushButton("保存检测报告")
        self.save_report_btn.clicked.connect(self.save_detection_report)
        self.save_report_btn.setEnabled(False)
        export_layout.addWidget(self.save_report_btn)
        
        layout.addWidget(export_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        # 初始化算法选择
        self.on_algorithm_type_changed("传统算法")
        
        return panel
    
    def create_display_area(self):
        """创建显示区域"""
        display_widget = QWidget()
        layout = QVBoxLayout(display_widget)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 原始图像标签页
        self.original_tab = QWidget()
        original_layout = QVBoxLayout(self.original_tab)
        self.original_image_label = QLabel("请加载图像")
        self.original_image_label.setAlignment(Qt.AlignCenter)
        self.original_image_label.setMinimumSize(600, 400)
        self.original_image_label.setStyleSheet("border: 1px solid gray;")
        original_layout.addWidget(self.original_image_label)
        self.tab_widget.addTab(self.original_tab, "原始图像")
        
        # 检测结果标签页
        self.result_tab = QWidget()
        result_layout = QVBoxLayout(self.result_tab)
        self.result_image_label = QLabel("等待检测结果")
        self.result_image_label.setAlignment(Qt.AlignCenter)
        self.result_image_label.setMinimumSize(600, 400)
        self.result_image_label.setStyleSheet("border: 1px solid gray;")
        result_layout.addWidget(self.result_image_label)
        self.tab_widget.addTab(self.result_tab, "检测结果")
        
        # 统计信息区域
        stats_group = QGroupBox("检测统计")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_text = QTextEdit()
        self.stats_text.setMaximumHeight(150)
        self.stats_text.setReadOnly(True)
        stats_layout.addWidget(self.stats_text)
        
        layout.addWidget(stats_group)
        
        return display_widget

    def on_algorithm_type_changed(self, algorithm_type):
        """算法类型改变时的处理"""
        # 清空方法选择
        self.method_combo.clear()

        # 根据算法类型添加方法
        if algorithm_type == "传统算法":
            self.method_combo.addItems(["Otsu阈值", "K-means聚类", "组合方法"])
            self.current_algorithm_type = 'traditional'
        elif algorithm_type == "深度学习":
            self.method_combo.addItems(["图像分类", "YOLO检测", "U-Net分割", "集成方法"])
            self.current_algorithm_type = 'deep_learning'
        elif algorithm_type == "Transformer":
            self.method_combo.addItems(["ViT分类", "PCTNet分割", "集成方法"])
            self.current_algorithm_type = 'transformer'

        # 更新参数配置
        self.update_parameter_controls()

    def on_method_changed(self, method):
        """检测方法改变时的处理"""
        self.update_parameter_controls()

    def update_parameter_controls(self):
        """更新参数控制界面"""
        # 清空现有控件
        for i in reversed(range(self.params_layout.count())):
            child = self.params_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # 获取当前方法
        method_text = self.method_combo.currentText()
        if not method_text:
            return

        # 映射方法名称
        method_mapping = {
            "Otsu阈值": "otsu",
            "K-means聚类": "kmeans",
            "组合方法": "combined",
            "图像分类": "classification",
            "YOLO检测": "yolo",
            "U-Net分割": "unet",
            "集成方法": "ensemble",
            "ViT分类": "vit",
            "PCTNet分割": "pctnet"
        }

        method_key = method_mapping.get(method_text, "")
        if not method_key:
            return

        # 获取参数配置
        config = self.algorithm_configs.get(self.current_algorithm_type, {}).get(method_key, {})

        # 创建参数控件
        if not hasattr(self, 'param_widgets'):
            self.param_widgets = {}

        for param_name, default_value in config.items():
            if isinstance(default_value, bool):
                # 布尔参数
                checkbox = QCheckBox(param_name.replace('_', ' ').title())
                checkbox.setChecked(default_value)
                self.param_widgets[param_name] = checkbox
                self.params_layout.addWidget(checkbox)

            elif isinstance(default_value, int):
                # 整数参数
                label = QLabel(f"{param_name.replace('_', ' ').title()}:")
                spinbox = QSpinBox()
                spinbox.setRange(1, 1000)
                spinbox.setValue(default_value)
                self.param_widgets[param_name] = spinbox
                self.params_layout.addWidget(label)
                self.params_layout.addWidget(spinbox)

            elif isinstance(default_value, float):
                # 浮点参数
                label = QLabel(f"{param_name.replace('_', ' ').title()}:")
                spinbox = QDoubleSpinBox()
                spinbox.setRange(0.0, 1.0)
                spinbox.setDecimals(3)
                spinbox.setSingleStep(0.01)
                spinbox.setValue(default_value)
                self.param_widgets[param_name] = spinbox
                self.params_layout.addWidget(label)
                self.params_layout.addWidget(spinbox)

    def load_image(self):
        """加载图像"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图像文件", "",
            "图像文件 (*.jpg *.jpeg *.png *.bmp *.tiff)"
        )

        if file_path:
            # 读取图像
            self.current_image = cv2.imread(file_path)
            if self.current_image is None:
                QMessageBox.warning(self, "错误", "无法读取图像文件")
                return

            # 显示图像信息
            h, w = self.current_image.shape[:2]
            file_size = os.path.getsize(file_path) / 1024  # KB
            self.image_info_label.setText(
                f"尺寸: {w}×{h}\n"
                f"大小: {file_size:.1f} KB\n"
                f"文件: {os.path.basename(file_path)}"
            )

            # 显示原始图像
            self.display_image(self.current_image, self.original_image_label)

            # 启用检测按钮
            self.detect_btn.setEnabled(True)

            self.statusBar().showMessage(f"已加载图像: {os.path.basename(file_path)}")

    def display_image(self, cv_image, label):
        """在标签中显示OpenCV图像"""
        # 转换颜色空间
        if len(cv_image.shape) == 3:
            rgb_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
        else:
            rgb_image = cv_image

        # 获取标签尺寸
        label_size = label.size()

        # 计算缩放比例
        h, w = rgb_image.shape[:2]
        scale_w = label_size.width() / w
        scale_h = label_size.height() / h
        scale = min(scale_w, scale_h, 1.0)  # 不放大

        # 调整图像尺寸
        new_w = int(w * scale)
        new_h = int(h * scale)
        resized_image = cv2.resize(rgb_image, (new_w, new_h))

        # 转换为QImage
        if len(resized_image.shape) == 3:
            qimage = QImage(resized_image.data, new_w, new_h,
                           new_w * 3, QImage.Format_RGB888)
        else:
            qimage = QImage(resized_image.data, new_w, new_h,
                           new_w, QImage.Format_Grayscale8)

        # 显示图像
        pixmap = QPixmap.fromImage(qimage)
        label.setPixmap(pixmap)

    def get_current_parameters(self):
        """获取当前参数设置"""
        parameters = {}

        if hasattr(self, 'param_widgets') and self.param_widgets:
            for param_name, widget in self.param_widgets.items():
                if isinstance(widget, QCheckBox):
                    parameters[param_name] = widget.isChecked()
                elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
                    parameters[param_name] = widget.value()

        return parameters

    def start_detection(self):
        """开始检测"""
        if self.current_image is None:
            QMessageBox.warning(self, "错误", "请先加载图像")
            return

        # 获取当前选择的方法
        method_text = self.method_combo.currentText()
        method_mapping = {
            "Otsu阈值": "otsu",
            "K-means聚类": "kmeans",
            "组合方法": "combined",
            "图像分类": "classification",
            "YOLO检测": "yolo",
            "U-Net分割": "unet",
            "集成方法": "ensemble",
            "ViT分类": "vit",
            "PCTNet分割": "pctnet"
        }

        method = method_mapping.get(method_text, "")
        if not method:
            QMessageBox.warning(self, "错误", "无效的检测方法")
            return

        # 获取参数
        parameters = self.get_current_parameters()

        # 禁用检测按钮
        self.detect_btn.setEnabled(False)
        self.progress_bar.setValue(0)

        # 创建并启动工作线程
        self.detection_worker = DetectionWorker(
            self.current_image.copy(),
            self.current_algorithm_type,
            method,
            parameters
        )

        self.detection_worker.finished.connect(self.on_detection_finished)
        self.detection_worker.progress.connect(self.progress_bar.setValue)
        self.detection_worker.error.connect(self.on_detection_error)

        self.detection_worker.start()

        self.statusBar().showMessage("正在检测...")

    def on_detection_finished(self, result):
        """检测完成处理"""
        self.current_result = result

        # 重新启用检测按钮
        self.detect_btn.setEnabled(True)
        self.progress_bar.setValue(100)

        # 显示结果
        if 'error' in result:
            QMessageBox.warning(self, "检测错误", result['error'])
            self.statusBar().showMessage("检测失败")
            return

        # 显示检测结果图像
        if 'binary_mask' in result:
            # 创建可视化图像
            visualization = self.create_visualization(self.current_image, result)
            self.display_image(visualization, self.result_image_label)

            # 切换到结果标签页
            self.tab_widget.setCurrentIndex(1)

        # 显示统计信息
        self.display_statistics(result)

        # 启用导出按钮
        self.save_result_btn.setEnabled(True)
        self.save_report_btn.setEnabled(True)

        self.statusBar().showMessage("检测完成")

    def on_detection_error(self, error_msg):
        """检测错误处理"""
        self.detect_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        QMessageBox.critical(self, "检测错误", f"检测过程中发生错误:\n{error_msg}")
        self.statusBar().showMessage("检测失败")

    def create_visualization(self, original_image, result):
        """创建可视化图像"""
        # 复制原始图像
        if len(original_image.shape) == 3:
            visualization = original_image.copy()
        else:
            visualization = cv2.cvtColor(original_image, cv2.COLOR_GRAY2BGR)

        # 叠加检测结果
        if 'binary_mask' in result:
            mask = result['binary_mask']

            # 创建彩色掩码
            colored_mask = np.zeros_like(visualization)
            colored_mask[:, :, 2] = mask  # 红色通道

            # 半透明叠加
            alpha = 0.3
            visualization = cv2.addWeighted(visualization, 1-alpha, colored_mask, alpha, 0)

        # 绘制检测框（如果有）
        if 'detections' in result:
            for detection in result['detections']:
                bbox = detection['bbox']
                confidence = detection['confidence']

                x, y, w, h = bbox
                cv2.rectangle(visualization, (x, y), (x+w, y+h), (0, 255, 0), 2)

                # 添加置信度标签
                label = f"{confidence:.2f}"
                cv2.putText(visualization, label, (x, y-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        return visualization

    def display_statistics(self, result):
        """显示统计信息"""
        stats_text = "=== 检测统计信息 ===\n\n"

        # 基本信息
        stats_text += f"检测方法: {result.get('method', 'Unknown')}\n"

        if 'statistics' in result:
            stats = result['statistics']

            # 处理时间
            if 'processing_time' in stats:
                stats_text += f"处理时间: {stats['processing_time']:.3f} 秒\n"

            # 检测数量
            if 'crack_count' in stats:
                stats_text += f"裂缝数量: {stats['crack_count']}\n"
            elif 'detection_count' in stats:
                stats_text += f"检测数量: {stats['detection_count']}\n"
            elif 'patch_count' in stats:
                stats_text += f"裂缝块数量: {stats['patch_count']}\n"

            # 面积信息
            if 'total_area' in stats:
                stats_text += f"总面积: {stats['total_area']} 像素\n"
            elif 'crack_pixel_count' in stats:
                stats_text += f"裂缝像素数: {stats['crack_pixel_count']}\n"

            # 阈值信息
            if 'threshold' in stats:
                stats_text += f"阈值: {stats['threshold']}\n"
            elif 'confidence_threshold' in stats:
                stats_text += f"置信度阈值: {stats['confidence_threshold']}\n"

        # 像素标定信息
        pixel_scale = self.pixel_scale_spinbox.value()
        stats_text += f"\n=== 像素标定 ===\n"
        stats_text += f"像素比例: {pixel_scale:.6f} mm/pixel\n"

        # 计算物理尺寸（如果有面积信息）
        if 'statistics' in result:
            stats = result['statistics']
            if 'total_area' in stats:
                area_mm2 = stats['total_area'] * (pixel_scale ** 2)
                stats_text += f"实际面积: {area_mm2:.2f} mm²\n"
            elif 'crack_pixel_count' in stats:
                area_mm2 = stats['crack_pixel_count'] * (pixel_scale ** 2)
                stats_text += f"实际面积: {area_mm2:.2f} mm²\n"

        # 算法特定信息
        if result.get('method') == 'ensemble' or result.get('method') == 'transformer_ensemble':
            if 'individual_results' in result:
                stats_text += f"\n=== 集成方法详情 ===\n"
                for method, method_result in result['individual_results'].items():
                    if 'statistics' in method_result:
                        method_stats = method_result['statistics']
                        processing_time = method_stats.get('processing_time', 0)
                        stats_text += f"{method}: {processing_time:.3f}s\n"

        self.stats_text.setText(stats_text)

    def save_result_image(self):
        """保存结果图像"""
        if self.current_result is None:
            QMessageBox.warning(self, "错误", "没有检测结果可保存")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存结果图像",
            f"crack_detection_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg",
            "图像文件 (*.jpg *.png *.bmp)"
        )

        if file_path:
            try:
                # 创建可视化图像
                visualization = self.create_visualization(self.current_image, self.current_result)

                # 保存图像
                cv2.imwrite(file_path, visualization)

                QMessageBox.information(self, "成功", f"结果图像已保存到:\n{file_path}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存图像失败:\n{str(e)}")

    def save_detection_report(self):
        """保存检测报告"""
        if self.current_result is None:
            QMessageBox.warning(self, "错误", "没有检测结果可保存")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存检测报告",
            f"crack_detection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON文件 (*.json)"
        )

        if file_path:
            try:
                # 准备报告数据
                report_data = {
                    'timestamp': datetime.now().isoformat(),
                    'algorithm_type': self.current_algorithm_type,
                    'method': self.method_combo.currentText(),
                    'parameters': self.get_current_parameters(),
                    'pixel_scale': self.pixel_scale_spinbox.value(),
                    'image_info': {
                        'size': self.current_image.shape[:2] if self.current_image is not None else None
                    },
                    'detection_result': {
                        'method': self.current_result.get('method'),
                        'statistics': self.current_result.get('statistics', {}),
                        'crack_info': self.current_result.get('crack_info', [])
                    }
                }

                # 保存报告
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(report_data, f, indent=2, ensure_ascii=False)

                QMessageBox.information(self, "成功", f"检测报告已保存到:\n{file_path}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存报告失败:\n{str(e)}")


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("混凝土裂缝检测算法选择系统")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("裂缝检测实验室")

    # 创建主窗口
    window = AlgorithmSelectionGUI()
    window.show()

    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
