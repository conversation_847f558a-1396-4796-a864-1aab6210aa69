# -*- coding: utf-8 -*-
"""
快速启动脚本
提供简单的命令行接口来快速使用各种裂缝检测算法

使用方法:
python quick_start.py --help
python quick_start.py --gui                    # 启动GUI界面
python quick_start.py --demo                   # 运行演示
python quick_start.py --image path/to/image    # 检测单个图像
python quick_start.py --batch path/to/folder   # 批量检测
"""

import sys
import os
import argparse
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def run_gui():
    """启动GUI界面"""
    try:
        import warnings
        warnings.filterwarnings("ignore", message=".*NumPy.*")
        warnings.filterwarnings("ignore", category=UserWarning)

        print("启动GUI界面...")

        # 使用简化的启动方式
        import subprocess
        import sys

        # 启动简化的GUI脚本
        result = subprocess.run([
            sys.executable, "launch_gui.py"
        ], cwd=current_dir)

        if result.returncode != 0:
            print("GUI启动失败，尝试备用方法...")
            # 备用方法：直接导入
            from algorithm_selection_gui import AlgorithmSelectionGUI
            from PyQt5.QtWidgets import QApplication

            app = QApplication(sys.argv)
            window = AlgorithmSelectionGUI()
            window.show()
            app.exec_()

    except ImportError as e:
        print(f"GUI依赖缺失: {e}")
        print("请安装PyQt5: pip install PyQt5")
        print("请检查NumPy版本: pip install 'numpy<2.0'")
    except Exception as e:
        print(f"启动GUI失败: {e}")
        print("请尝试直接运行: python launch_gui.py")

def run_demo():
    """运行演示"""
    print("=== 裂缝检测算法演示 ===\n")
    
    # 创建测试图像目录
    test_dir = Path("test_images")
    test_dir.mkdir(exist_ok=True)
    
    # 检查是否有测试图像
    test_images = list(test_dir.glob("*.jpg")) + list(test_dir.glob("*.png"))
    
    if not test_images:
        print("未找到测试图像。请在 test_images/ 目录中放置一些裂缝图像。")
        print("支持的格式: .jpg, .png")
        return
    
    print(f"找到 {len(test_images)} 个测试图像")
    
    # 运行传统算法演示
    print("\n1. 传统算法演示:")
    try:
        from traditional_algorithms import demo_traditional_algorithms
        demo_traditional_algorithms()
    except Exception as e:
        print(f"传统算法演示失败: {e}")
    
    # 运行深度学习算法演示
    print("\n2. 深度学习算法演示:")
    try:
        from deep_learning_algorithms import demo_deep_learning_algorithms
        demo_deep_learning_algorithms()
    except Exception as e:
        print(f"深度学习算法演示失败: {e}")
    
    # 运行Transformer算法演示
    print("\n3. Transformer算法演示:")
    try:
        from transformer_models import demo_transformer_models
        demo_transformer_models()
    except Exception as e:
        print(f"Transformer算法演示失败: {e}")
    
    # 运行像素标定演示
    print("\n4. 像素标定演示:")
    try:
        from pixel_calibration import demo_pixel_calibration
        demo_pixel_calibration()
    except Exception as e:
        print(f"像素标定演示失败: {e}")
    
    print("\n演示完成！结果已保存到 output/ 目录")

def detect_single_image(image_path: str, algorithm: str = "all"):
    """检测单个图像"""
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        return
    
    print(f"检测图像: {image_path}")
    print(f"使用算法: {algorithm}")
    
    try:
        from comprehensive_crack_detection_system import ComprehensiveCrackDetectionSystem
        
        # 创建检测系统
        system = ComprehensiveCrackDetectionSystem()
        
        # 确定要使用的算法
        if algorithm == "all":
            algorithms = ['traditional', 'deep_learning', 'transformer']
        elif algorithm == "traditional":
            algorithms = ['traditional']
        elif algorithm == "deep_learning":
            algorithms = ['deep_learning']
        elif algorithm == "transformer":
            algorithms = ['transformer']
        else:
            print(f"不支持的算法: {algorithm}")
            return
        
        # 执行检测
        result = system.detect_single_image(image_path, algorithms)
        
        print("\n检测完成！")
        print(f"结果已保存到: output/")
        
        # 显示简要统计
        if 'comparison' in result:
            comparison = result['comparison']
            if 'processing_times' in comparison:
                print("\n处理时间:")
                for alg_type, methods in comparison['processing_times'].items():
                    for method, time in methods.items():
                        print(f"  {alg_type}-{method}: {time:.3f}s")
        
    except Exception as e:
        print(f"检测失败: {e}")

def batch_detection(folder_path: str, algorithm: str = "all"):
    """批量检测"""
    if not os.path.exists(folder_path):
        print(f"文件夹不存在: {folder_path}")
        return
    
    print(f"批量检测文件夹: {folder_path}")
    print(f"使用算法: {algorithm}")
    
    try:
        from comprehensive_crack_detection_system import ComprehensiveCrackDetectionSystem
        
        # 创建检测系统
        system = ComprehensiveCrackDetectionSystem()
        
        # 确定要使用的算法
        if algorithm == "all":
            algorithms = ['traditional', 'deep_learning', 'transformer']
        elif algorithm == "traditional":
            algorithms = ['traditional']
        elif algorithm == "deep_learning":
            algorithms = ['deep_learning']
        elif algorithm == "transformer":
            algorithms = ['transformer']
        else:
            print(f"不支持的算法: {algorithm}")
            return
        
        # 执行批量检测
        results = system.batch_detection(folder_path, algorithms)
        
        print(f"\n批量检测完成！")
        print(f"总图像数: {results['total_images']}")
        print(f"成功处理: {results['processed_images']}")
        print(f"处理失败: {results['failed_images']}")
        print(f"成功率: {results['processed_images']/results['total_images']*100:.1f}%")
        print(f"结果已保存到: output/")
        
    except Exception as e:
        print(f"批量检测失败: {e}")

def check_dependencies():
    """检查依赖"""
    print("检查依赖包...")
    
    required_packages = [
        'cv2', 'numpy', 'torch', 'torchvision', 'sklearn', 
        'skimage', 'matplotlib', 'ultralytics'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'sklearn':
                import sklearn
            elif package == 'skimage':
                import skimage
            else:
                __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        print("请使用以下命令安装:")
        
        install_commands = {
            'cv2': 'pip install opencv-python',
            'torch': 'pip install torch torchvision torchaudio',
            'sklearn': 'pip install scikit-learn',
            'skimage': 'pip install scikit-image',
            'ultralytics': 'pip install ultralytics'
        }
        
        for package in missing_packages:
            if package in install_commands:
                print(f"  {install_commands[package]}")
            else:
                print(f"  pip install {package}")
    else:
        print("\n所有依赖包都已安装！")

def show_help():
    """显示帮助信息"""
    help_text = """
=== 混凝土裂缝检测系统 ===

这是一个综合的混凝土裂缝检测系统，集成了多种算法：

1. 传统算法：
   - Otsu阈值分割
   - K-means聚类
   - Skele-Marker去噪

2. 深度学习算法：
   - 图像分类 (ResNet)
   - 目标检测 (YOLO)
   - 语义分割 (U-Net)

3. Transformer算法：
   - Vision Transformer (ViT)
   - PCTNet分割网络

4. 像素标定：
   - 相机标定
   - 尺寸量化

使用方法：

1. 启动GUI界面（推荐）：
   python quick_start.py --gui

2. 运行演示：
   python quick_start.py --demo

3. 检测单个图像：
   python quick_start.py --image path/to/image.jpg
   python quick_start.py --image path/to/image.jpg --algorithm traditional

4. 批量检测：
   python quick_start.py --batch path/to/folder
   python quick_start.py --batch path/to/folder --algorithm deep_learning

5. 检查依赖：
   python quick_start.py --check

支持的算法选项：
- all (默认): 运行所有算法
- traditional: 仅传统算法
- deep_learning: 仅深度学习算法
- transformer: 仅Transformer算法

输出目录结构：
output/
├── traditional/     # 传统算法结果
├── deep_learning/   # 深度学习结果
├── transformer/     # Transformer结果
├── comparison/      # 算法对比图
└── reports/         # 检测报告

更多信息请参考各模块的文档。
"""
    print(help_text)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="混凝土裂缝检测系统快速启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument("--gui", action="store_true", 
                       help="启动GUI界面")
    parser.add_argument("--demo", action="store_true",
                       help="运行演示")
    parser.add_argument("--image", type=str,
                       help="检测单个图像")
    parser.add_argument("--batch", type=str,
                       help="批量检测文件夹")
    parser.add_argument("--algorithm", "-a", 
                       choices=['all', 'traditional', 'deep_learning', 'transformer'],
                       default='all',
                       help="选择算法")
    parser.add_argument("--check", action="store_true",
                       help="检查依赖包")
    parser.add_argument("--help-detailed", action="store_true",
                       help="显示详细帮助")
    
    args = parser.parse_args()
    
    # 如果没有参数，显示帮助
    if len(sys.argv) == 1:
        show_help()
        return
    
    # 处理各种命令
    if args.help_detailed:
        show_help()
    elif args.check:
        check_dependencies()
    elif args.gui:
        run_gui()
    elif args.demo:
        run_demo()
    elif args.image:
        detect_single_image(args.image, args.algorithm)
    elif args.batch:
        batch_detection(args.batch, args.algorithm)
    else:
        print("请指定要执行的操作。使用 --help 查看帮助。")

if __name__ == "__main__":
    main()
