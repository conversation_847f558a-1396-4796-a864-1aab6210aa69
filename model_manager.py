# -*- coding: utf-8 -*-
"""
分割模型管理器

功能：
1. 支持多种YOLO分割模型选择
2. 自动模型下载和管理
3. 模型性能和特性对比
4. 智能模型推荐
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import requests
from datetime import datetime

try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False


class ModelManager:
    """分割模型管理器"""
    
    def __init__(self, models_dir="models"):
        """初始化模型管理器
        :param models_dir: 模型存储目录
        """
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(exist_ok=True)
        
        # 支持的分割模型配置
        self.available_models = {
            # YOLOv11 系列 (最新推荐)
            "yolo11n-seg": {
                "name": "YOLO11n-seg",
                "description": "YOLO11 Nano 分割模型 - 超轻量级，适合实时检测",
                "file": "yolo11n-seg.pt",
                "size_mb": 6.7,
                "speed_ms": 1.5,
                "accuracy": "中等",
                "recommended_for": ["实时检测", "资源受限环境", "快速原型"],
                "download_url": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n-seg.pt"
            },
            "yolo11s-seg": {
                "name": "YOLO11s-seg", 
                "description": "YOLO11 Small 分割模型 - 平衡速度和精度",
                "file": "yolo11s-seg.pt",
                "size_mb": 21.5,
                "speed_ms": 2.3,
                "accuracy": "良好",
                "recommended_for": ["通用检测", "平衡性能", "中等精度要求"],
                "download_url": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11s-seg.pt"
            },
            "yolo11m-seg": {
                "name": "YOLO11m-seg",
                "description": "YOLO11 Medium 分割模型 - 高精度检测",
                "file": "yolo11m-seg.pt", 
                "size_mb": 49.7,
                "speed_ms": 4.2,
                "accuracy": "高",
                "recommended_for": ["高精度检测", "专业应用", "质量优先"],
                "download_url": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11m-seg.pt"
            },
            "yolo11l-seg": {
                "name": "YOLO11l-seg",
                "description": "YOLO11 Large 分割模型 - 超高精度",
                "file": "yolo11l-seg.pt",
                "size_mb": 86.9,
                "speed_ms": 6.8,
                "accuracy": "很高",
                "recommended_for": ["超高精度", "研究应用", "最佳质量"],
                "download_url": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11l-seg.pt"
            },
            "yolo11x-seg": {
                "name": "YOLO11x-seg",
                "description": "YOLO11 Extra Large 分割模型 - 极致精度",
                "file": "yolo11x-seg.pt",
                "size_mb": 140.4,
                "speed_ms": 11.3,
                "accuracy": "极高",
                "recommended_for": ["极致精度", "科研级应用", "无速度限制"],
                "download_url": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11x-seg.pt"
            },
            
            # YOLOv8 系列 (稳定版本)
            "yolov8n-seg": {
                "name": "YOLOv8n-seg",
                "description": "YOLOv8 Nano 分割模型 - 稳定轻量级",
                "file": "yolov8n-seg.pt",
                "size_mb": 6.7,
                "speed_ms": 1.8,
                "accuracy": "中等",
                "recommended_for": ["稳定版本", "兼容性优先", "轻量级应用"],
                "download_url": "https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8n-seg.pt"
            },
            "yolov8s-seg": {
                "name": "YOLOv8s-seg",
                "description": "YOLOv8 Small 分割模型 - 稳定平衡版",
                "file": "yolov8s-seg.pt",
                "size_mb": 21.5,
                "speed_ms": 2.8,
                "accuracy": "良好",
                "recommended_for": ["稳定版本", "生产环境", "可靠性优先"],
                "download_url": "https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8s-seg.pt"
            },
            "yolov8m-seg": {
                "name": "YOLOv8m-seg",
                "description": "YOLOv8 Medium 分割模型 - 稳定高精度",
                "file": "yolov8m-seg.pt",
                "size_mb": 49.7,
                "speed_ms": 5.1,
                "accuracy": "高",
                "recommended_for": ["稳定版本", "高精度需求", "成熟应用"],
                "download_url": "https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8m-seg.pt"
            },
            
            # 专业定制模型 (如果有的话)
            "custom-crack-seg": {
                "name": "Custom Crack Segmentation",
                "description": "专门训练的裂缝分割模型 - 针对裂缝优化",
                "file": "custom-crack-seg.pt",
                "size_mb": 0,  # 未知大小
                "speed_ms": 0,  # 未知速度
                "accuracy": "专业",
                "recommended_for": ["裂缝专用", "最佳裂缝检测", "专业应用"],
                "download_url": None,  # 需要用户提供
                "is_custom": True
            }
        }
        
        # 加载模型状态
        self.model_status = self.load_model_status()
    
    def load_model_status(self) -> Dict:
        """加载模型状态信息"""
        status_file = self.models_dir / "model_status.json"
        if status_file.exists():
            try:
                with open(status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
    
    def save_model_status(self):
        """保存模型状态信息"""
        status_file = self.models_dir / "model_status.json"
        try:
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(self.model_status, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存模型状态失败: {e}")
    
    def get_available_models(self) -> Dict:
        """获取所有可用模型信息"""
        return self.available_models.copy()
    
    def get_installed_models(self) -> List[str]:
        """获取已安装的模型列表"""
        installed = []
        for model_id, config in self.available_models.items():
            model_path = self.models_dir / config["file"]
            if model_path.exists():
                installed.append(model_id)
        return installed
    
    def get_model_path(self, model_id: str) -> Optional[Path]:
        """获取模型文件路径"""
        if model_id not in self.available_models:
            return None
        
        config = self.available_models[model_id]
        model_path = self.models_dir / config["file"]
        
        if model_path.exists():
            return model_path
        return None
    
    def is_model_installed(self, model_id: str) -> bool:
        """检查模型是否已安装"""
        return self.get_model_path(model_id) is not None
    
    def download_model(self, model_id: str, progress_callback=None) -> bool:
        """下载指定模型
        :param model_id: 模型ID
        :param progress_callback: 进度回调函数
        :return: 是否下载成功
        """
        if model_id not in self.available_models:
            print(f"未知模型: {model_id}")
            return False
        
        config = self.available_models[model_id]
        
        # 检查是否为自定义模型
        if config.get("is_custom", False):
            print(f"自定义模型 {model_id} 需要用户手动提供")
            return False
        
        if not config.get("download_url"):
            print(f"模型 {model_id} 没有下载链接")
            return False
        
        model_path = self.models_dir / config["file"]
        
        # 如果已存在，跳过下载
        if model_path.exists():
            print(f"模型 {config['name']} 已存在")
            return True
        
        print(f"开始下载模型: {config['name']}")
        print(f"大小: {config['size_mb']} MB")
        
        try:
            # 使用YOLO自动下载功能
            if YOLO_AVAILABLE:
                print(f"正在下载模型 {config['file']}...")

                # 直接使用YOLO加载，它会自动下载
                model = YOLO(config["file"])

                # 检查多个可能的位置
                possible_paths = [
                    Path.home() / '.ultralytics' / 'models' / config["file"],
                    Path.cwd() / config["file"],
                    Path(config["file"])
                ]

                source_path = None
                for path in possible_paths:
                    if path.exists():
                        source_path = path
                        break

                if source_path:
                    # 将模型复制到我们的目录
                    import shutil
                    shutil.copy2(source_path, model_path)
                    print(f"模型下载完成: {model_path}")

                    # 更新状态
                    self.model_status[model_id] = {
                        "downloaded_at": datetime.now().isoformat(),
                        "file_size": model_path.stat().st_size,
                        "version": "latest",
                        "source_path": str(source_path)
                    }
                    self.save_model_status()
                    return True
                else:
                    # 如果找不到文件，但模型加载成功，说明已经在缓存中
                    print(f"模型已在YOLO缓存中，创建符号链接...")
                    # 创建一个标记文件表示模型可用
                    with open(model_path, 'w') as f:
                        f.write(f"# YOLO model {config['file']} is available in cache\n")
                        f.write(f"# Downloaded at: {datetime.now().isoformat()}\n")

                    self.model_status[model_id] = {
                        "downloaded_at": datetime.now().isoformat(),
                        "file_size": 0,  # 缓存中的模型
                        "version": "latest",
                        "cached": True
                    }
                    self.save_model_status()
                    print(f"模型可用: {config['file']} (YOLO缓存)")
                    return True
            else:
                print("YOLO不可用，无法下载模型")
                return False
                
        except Exception as e:
            print(f"下载模型失败: {e}")
            return False
    
    def recommend_model(self, use_case: str = "general") -> str:
        """推荐合适的模型
        :param use_case: 使用场景 ("speed", "accuracy", "balanced", "general")
        :return: 推荐的模型ID
        """
        if use_case == "speed":
            return "yolo11n-seg"  # 最快
        elif use_case == "accuracy":
            return "yolo11l-seg"  # 高精度
        elif use_case == "balanced":
            return "yolo11s-seg"  # 平衡
        elif use_case == "crack_specific":
            # 如果有自定义裂缝模型且已安装
            if self.is_model_installed("custom-crack-seg"):
                return "custom-crack-seg"
            else:
                return "yolo11m-seg"  # 高精度通用模型
        else:  # general
            return "yolo11s-seg"  # 默认推荐
    
    def get_model_info(self, model_id: str) -> Optional[Dict]:
        """获取模型详细信息"""
        if model_id not in self.available_models:
            return None
        
        config = self.available_models[model_id].copy()
        config["installed"] = self.is_model_installed(model_id)
        config["model_path"] = str(self.get_model_path(model_id)) if config["installed"] else None
        
        # 添加状态信息
        if model_id in self.model_status:
            config["status"] = self.model_status[model_id]
        
        return config
    
    def load_model(self, model_id: str):
        """加载指定模型
        :param model_id: 模型ID
        :return: YOLO模型实例或None
        """
        if not YOLO_AVAILABLE:
            print("YOLO不可用")
            return None

        if model_id not in self.available_models:
            print(f"未知模型ID: {model_id}")
            return None

        config = self.available_models[model_id]

        # 检查是否已安装
        model_path = self.get_model_path(model_id)

        try:
            if model_path and model_path.exists():
                # 检查是否是缓存标记文件
                if model_path.stat().st_size < 1000:  # 小于1KB，可能是标记文件
                    # 直接使用YOLO文件名加载（从缓存）
                    model = YOLO(config["file"])
                else:
                    # 从本地文件加载
                    model = YOLO(str(model_path))
            else:
                # 尝试直接加载（可能在YOLO缓存中）
                model = YOLO(config["file"])

            print(f"成功加载模型: {config['name']}")
            return model
        except Exception as e:
            print(f"加载模型失败: {e}")
            return None
    
    def add_custom_model(self, model_id: str, model_path: str, name: str, description: str):
        """添加自定义模型
        :param model_id: 模型ID
        :param model_path: 模型文件路径
        :param name: 模型名称
        :param description: 模型描述
        """
        # 复制模型到管理目录
        import shutil
        source_path = Path(model_path)
        if not source_path.exists():
            print(f"模型文件不存在: {model_path}")
            return False
        
        target_path = self.models_dir / f"{model_id}.pt"
        shutil.copy2(source_path, target_path)
        
        # 添加到配置
        self.available_models[model_id] = {
            "name": name,
            "description": description,
            "file": f"{model_id}.pt",
            "size_mb": target_path.stat().st_size / (1024 * 1024),
            "speed_ms": 0,  # 未知
            "accuracy": "自定义",
            "recommended_for": ["自定义应用"],
            "download_url": None,
            "is_custom": True
        }
        
        # 更新状态
        self.model_status[model_id] = {
            "added_at": datetime.now().isoformat(),
            "file_size": target_path.stat().st_size,
            "source_path": str(source_path)
        }
        self.save_model_status()
        
        print(f"自定义模型添加成功: {name}")
        return True
    
    def remove_model(self, model_id: str) -> bool:
        """删除模型
        :param model_id: 模型ID
        :return: 是否删除成功
        """
        model_path = self.get_model_path(model_id)
        if not model_path:
            print(f"模型 {model_id} 未安装")
            return False
        
        try:
            model_path.unlink()
            if model_id in self.model_status:
                del self.model_status[model_id]
            self.save_model_status()
            print(f"模型删除成功: {model_id}")
            return True
        except Exception as e:
            print(f"删除模型失败: {e}")
            return False
    
    def print_model_summary(self):
        """打印模型摘要信息"""
        print("\n=== 分割模型管理器 ===")
        print(f"模型存储目录: {self.models_dir}")
        print(f"可用模型数量: {len(self.available_models)}")
        print(f"已安装模型: {len(self.get_installed_models())}")
        
        print("\n模型列表:")
        for model_id, config in self.available_models.items():
            status = "✅ 已安装" if self.is_model_installed(model_id) else "❌ 未安装"
            print(f"  {model_id}: {config['name']} - {status}")
            print(f"    描述: {config['description']}")
            print(f"    大小: {config['size_mb']} MB, 速度: {config['speed_ms']} ms")
            print()


# 全局模型管理器实例
model_manager = ModelManager()


if __name__ == "__main__":
    # 测试模型管理器
    print("=== 分割模型管理器测试 ===")
    
    # 打印模型摘要
    model_manager.print_model_summary()
    
    # 推荐模型
    print(f"速度优先推荐: {model_manager.recommend_model('speed')}")
    print(f"精度优先推荐: {model_manager.recommend_model('accuracy')}")
    print(f"平衡推荐: {model_manager.recommend_model('balanced')}")
    
    # 尝试下载推荐模型
    recommended = model_manager.recommend_model('balanced')
    if not model_manager.is_model_installed(recommended):
        print(f"\n尝试下载推荐模型: {recommended}")
        success = model_manager.download_model(recommended)
        if success:
            print("下载成功！")
        else:
            print("下载失败！")
