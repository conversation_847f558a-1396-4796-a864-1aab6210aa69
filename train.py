# -*- coding: utf-8 -*-
"""
YOLO裂缝检测训练脚本
支持GPU/CPU自动检测和优化配置
"""

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'

import warnings
warnings.filterwarnings('ignore')

import torch
from ultralytics import YOLO

def get_device_config():
    """获取设备配置"""
    if torch.cuda.is_available():
        device_count = torch.cuda.device_count()
        gpu_name = torch.cuda.get_device_name(0)
        memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3

        print(f"✅ 检测到GPU: {gpu_name} ({memory_gb:.1f} GB)")
        print(f"GPU数量: {device_count}")

        # 根据显存大小调整配置
        if memory_gb >= 20:  # RTX A5000 24GB
            config = {
                'device': '0',
                'batch_size': 16,  # 大显存可以用更大批次
                'workers': 8,
                'cache': True,     # 启用缓存
                'amp': True,       # 混合精度训练
                'optimizer': 'AdamW'
            }
            print("🚀 大显存GPU配置 (高性能)")
        elif memory_gb >= 8:   # RTX 3070/4060等
            config = {
                'device': '0',
                'batch_size': 8,
                'workers': 4,
                'cache': True,
                'amp': True,
                'optimizer': 'AdamW'
            }
            print("⚡ 中等显存GPU配置")
        else:  # 小显存GPU
            config = {
                'device': '0',
                'batch_size': 4,
                'workers': 2,
                'cache': False,
                'amp': True,
                'optimizer': 'SGD'
            }
            print("💾 小显存GPU配置")

        return config
    else:
        print("❌ 未检测到CUDA GPU，强制使用GPU模式")
        print("请确保已安装GPU版本的PyTorch")
        # 强制使用GPU配置，让用户知道需要GPU
        return {
            'device': '0',  # 强制GPU
            'batch_size': 16,
            'workers': 8,
            'cache': True,
            'amp': True,
            'optimizer': 'AdamW'
        }

if __name__ == '__main__':
    print("=== GPU YOLO裂缝检测模型训练 ===")

    # 获取GPU配置
    config = get_device_config()

    # 加载模型 - 优先使用预训练模型
    pretrained_model_path = r'E:\yolo\ultralytics-main\ultralytics-main-max-area\yolo11n-seg.pt'
    yaml_model_path = r'E:\yolo\ultralytics-main\ultralytics-main-max-area\ultralytics\cfg\models\11\yolo11-seg.yaml'

    if os.path.exists(pretrained_model_path):
        model = YOLO(pretrained_model_path)
        print(f"✅ 加载预训练模型: {pretrained_model_path}")
    elif os.path.exists(yaml_model_path):
        model = YOLO(yaml_model_path)
        print(f"✅ 加载YAML配置: {yaml_model_path}")
    else:
        print("⚠️  本地模型文件不存在，将自动下载")
        model = YOLO('yolo11n-seg.pt')
        print("✅ 下载并加载预训练模型: yolo11n-seg.pt")

    print(f"\n🚀 开始GPU训练...")
    print(f"设备: {config['device']}")
    print(f"批次大小: {config['batch_size']}")
    print(f"工作进程: {config['workers']}")
    print(f"混合精度: {config['amp']}")
    print(f"优化器: {config['optimizer']}")

    # GPU训练参数
    train_args = {
        'data': r'E:\yolo\ultralytics-main\ultralytics-main-max-area\data.yaml',
        'imgsz': 640,
        'epochs': 300,
        'batch': config['batch_size'],
        'workers': config['workers'],
        'device': config['device'],
        'optimizer': config['optimizer'],
        'lr0': 0.01,           # 初始学习率
        'lrf': 0.01,           # 最终学习率因子
        'momentum': 0.937,     # 动量
        'weight_decay': 0.0005, # 权重衰减
        'warmup_epochs': 3,    # 预热轮数
        'close_mosaic': 10,
        'resume': False,
        'project': 'runs/train',
        'name': 'crack_detection_gpu',
        'single_cls': False,
        'cache': config['cache'],
        'amp': config['amp'],  # 混合精度训练
        'save_period': 10,     # 每10轮保存一次
        'patience': 50,        # 早停耐心值
        'verbose': True,
        'plots': True,         # 生成训练图表
        'val': True,           # 启用验证
        'multi_scale': True,   # 多尺度训练
        'overlap_mask': True,  # 重叠掩码
        'mask_ratio': 4,       # 掩码比例
        'dropout': 0.0,        # Dropout率
        'label_smoothing': 0.0, # 标签平滑
        'hsv_h': 0.015,        # HSV色调增强
        'hsv_s': 0.7,          # HSV饱和度增强
        'hsv_v': 0.4,          # HSV明度增强
        'degrees': 0.0,        # 旋转角度
        'translate': 0.1,      # 平移
        'scale': 0.5,          # 缩放
        'shear': 0.0,          # 剪切
        'perspective': 0.0,    # 透视变换
        'flipud': 0.0,         # 上下翻转概率
        'fliplr': 0.5,         # 左右翻转概率
        'mosaic': 1.0,         # 马赛克增强概率
        'mixup': 0.0,          # Mixup增强概率
        'copy_paste': 0.0,     # 复制粘贴增强概率
    }

    try:
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # 开始训练
        results = model.train(**train_args)

        print("✅ 训练完成!")
        print(f"最佳模型保存在: {results.save_dir}")

        # 显示最终结果
        if hasattr(results, 'results_dict'):
            print("\n📊 最终训练结果:")
            for key, value in results.results_dict.items():
                if isinstance(value, (int, float)):
                    print(f"  {key}: {value:.4f}")
                else:
                    print(f"  {key}: {value}")

    except Exception as e:
        print(f"❌ 训练失败: {e}")
        print("\n🔧 故障排除建议:")
        print("1. 确保已安装GPU版本的PyTorch:")
        print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121")
        print("2. 检查CUDA驱动是否正确安装")
        print("3. 尝试减小批次大小")
        print("4. 检查数据集路径是否正确")

    finally:
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
