# -*- coding: utf-8 -*-
"""
路面裂缝检测专业模块

功能：
1. 图像预处理：去噪、灰度化、二值化等操作
2. 裂缝检测：Canny边缘检测 + Gabor滤波器纹理检测
3. 裂缝分割：精确分割裂缝区域
4. 长度计算：骨架提取 + 长度测量
5. 宽度计算：多点采样 + 平均宽度计算
6. 面积计算：像素统计 + 实际面积转换
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from skimage.morphology import skeletonize, thin
from skimage import measure, filters
from scipy import ndimage
from datetime import datetime
import os
import csv


class PavementCrackDetector:
    def __init__(self, scale_factor=1.0, unit='mm'):
        """初始化路面裂缝检测器
        :param scale_factor: 比例尺（像素/实际单位）
        :param unit: 长度单位，默认为毫米
        """
        self.scale_factor = scale_factor
        self.unit = unit
        
        # 图像预处理参数
        self.preprocess_params = {
            'gaussian_kernel': 5,      # 高斯滤波核大小
            'gaussian_sigma': 1.0,     # 高斯滤波标准差
            'median_kernel': 3,        # 中值滤波核大小
            'bilateral_d': 9,          # 双边滤波邻域直径
            'bilateral_sigma_color': 75,  # 双边滤波颜色空间标准差
            'bilateral_sigma_space': 75,  # 双边滤波坐标空间标准差
        }
        
        # Canny边缘检测参数
        self.canny_params = {
            'low_threshold': 50,       # Canny低阈值
            'high_threshold': 150,     # Canny高阈值
            'aperture_size': 3,        # Sobel算子孔径大小
            'l2_gradient': True        # 使用L2梯度
        }
        
        # Gabor滤波器参数
        self.gabor_params = {
            'frequencies': [0.1, 0.3, 0.5],  # 频率列表
            'orientations': [0, 45, 90, 135],  # 方向列表（度）
            'sigma_x': 2.0,            # X方向标准差
            'sigma_y': 2.0,            # Y方向标准差
        }
        
        # 二值化参数
        self.binarization_params = {
            'method': 'adaptive',      # 二值化方法：'otsu', 'adaptive', 'manual'
            'threshold_value': 127,    # 手动阈值
            'adaptive_method': cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            'adaptive_type': cv2.THRESH_BINARY_INV,
            'block_size': 11,          # 自适应阈值块大小
            'c_constant': 2            # 自适应阈值常数
        }
        
        # 形态学处理参数
        self.morphology_params = {
            'opening_kernel': 3,       # 开运算核大小
            'closing_kernel': 5,       # 闭运算核大小
            'dilation_kernel': 3,      # 膨胀核大小
            'erosion_kernel': 2        # 腐蚀核大小
        }

    def advanced_preprocess(self, image):
        """高级图像预处理
        包括去噪、灰度化、增强等操作
        :param image: 输入图像
        :return: 预处理后的图像和处理步骤
        """
        steps = {}
        
        # 1. 灰度化
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        steps['1_grayscale'] = gray.copy()
        
        # 2. 高斯去噪
        gaussian_filtered = cv2.GaussianBlur(
            gray, 
            (self.preprocess_params['gaussian_kernel'], self.preprocess_params['gaussian_kernel']),
            self.preprocess_params['gaussian_sigma']
        )
        steps['2_gaussian_filtered'] = gaussian_filtered.copy()
        
        # 3. 中值滤波去噪
        median_filtered = cv2.medianBlur(
            gaussian_filtered, 
            self.preprocess_params['median_kernel']
        )
        steps['3_median_filtered'] = median_filtered.copy()
        
        # 4. 双边滤波保边去噪
        bilateral_filtered = cv2.bilateralFilter(
            median_filtered,
            self.preprocess_params['bilateral_d'],
            self.preprocess_params['bilateral_sigma_color'],
            self.preprocess_params['bilateral_sigma_space']
        )
        steps['4_bilateral_filtered'] = bilateral_filtered.copy()
        
        # 5. 对比度增强 (CLAHE)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(bilateral_filtered)
        steps['5_enhanced'] = enhanced.copy()
        
        return enhanced, steps

    def canny_edge_detection(self, image):
        """Canny边缘检测
        :param image: 预处理后的灰度图像
        :return: 边缘检测结果
        """
        edges = cv2.Canny(
            image,
            self.canny_params['low_threshold'],
            self.canny_params['high_threshold'],
            apertureSize=self.canny_params['aperture_size'],
            L2gradient=self.canny_params['l2_gradient']
        )
        return edges

    def gabor_filter_detection(self, image):
        """Gabor滤波器纹理检测
        :param image: 预处理后的灰度图像
        :return: Gabor滤波结果
        """
        responses = []
        
        for freq in self.gabor_params['frequencies']:
            for angle in self.gabor_params['orientations']:
                # 转换角度为弧度
                theta = np.deg2rad(angle)
                
                # 应用Gabor滤波器
                real, _ = filters.gabor(
                    image, 
                    frequency=freq, 
                    theta=theta,
                    sigma_x=self.gabor_params['sigma_x'],
                    sigma_y=self.gabor_params['sigma_y']
                )
                responses.append(np.abs(real))
        
        # 合并所有响应
        gabor_response = np.maximum.reduce(responses)
        
        # 归一化到0-255
        gabor_response = ((gabor_response - gabor_response.min()) / 
                         (gabor_response.max() - gabor_response.min()) * 255).astype(np.uint8)
        
        return gabor_response

    def advanced_binarization(self, image):
        """高级二值化处理
        :param image: 输入灰度图像
        :return: 二值化结果
        """
        method = self.binarization_params['method']
        
        if method == 'otsu':
            # Otsu自动阈值
            _, binary = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        elif method == 'adaptive':
            # 自适应阈值
            binary = cv2.adaptiveThreshold(
                image, 255,
                self.binarization_params['adaptive_method'],
                self.binarization_params['adaptive_type'],
                self.binarization_params['block_size'],
                self.binarization_params['c_constant']
            )
        else:  # manual
            # 手动阈值
            _, binary = cv2.threshold(
                image, 
                self.binarization_params['threshold_value'], 
                255, 
                cv2.THRESH_BINARY_INV
            )
        
        return binary

    def morphological_processing(self, binary_image):
        """形态学处理
        :param binary_image: 二值化图像
        :return: 形态学处理后的图像
        """
        # 开运算去除噪声
        opening_kernel = cv2.getStructuringElement(
            cv2.MORPH_ELLIPSE, 
            (self.morphology_params['opening_kernel'], self.morphology_params['opening_kernel'])
        )
        opened = cv2.morphologyEx(binary_image, cv2.MORPH_OPEN, opening_kernel)
        
        # 闭运算连接断裂
        closing_kernel = cv2.getStructuringElement(
            cv2.MORPH_ELLIPSE, 
            (self.morphology_params['closing_kernel'], self.morphology_params['closing_kernel'])
        )
        closed = cv2.morphologyEx(opened, cv2.MORPH_CLOSE, closing_kernel)
        
        return closed

    def crack_segmentation(self, binary_image):
        """裂缝分割
        将检测到的裂缝区域分割出来
        :param binary_image: 二值化图像
        :return: 分割后的裂缝区域和轮廓
        """
        # 查找轮廓
        contours, _ = cv2.findContours(binary_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 筛选有效轮廓
        valid_contours = []
        min_area = 50  # 最小面积阈值
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > min_area:
                # 计算轮廓的长宽比
                rect = cv2.minAreaRect(contour)
                width, height = rect[1]
                if min(width, height) > 0:
                    aspect_ratio = max(width, height) / min(width, height)
                    # 裂缝通常是细长的
                    if aspect_ratio > 2.0:
                        valid_contours.append(contour)
        
        # 创建分割掩码
        segmentation_mask = np.zeros_like(binary_image)
        cv2.fillPoly(segmentation_mask, valid_contours, 255)
        
        return segmentation_mask, valid_contours

    def calculate_crack_length(self, binary_crack_region):
        """计算裂缝长度
        通过骨架提取计算裂缝长度
        :param binary_crack_region: 二值化裂缝区域
        :return: 裂缝长度（像素和实际单位）
        """
        # 骨架提取
        skeleton = skeletonize(binary_crack_region > 0)
        
        # 计算骨架像素数量
        skeleton_pixels = np.sum(skeleton)
        
        # 转换为实际长度
        actual_length = skeleton_pixels * self.scale_factor
        
        return skeleton_pixels, actual_length, skeleton

    def calculate_crack_width(self, binary_crack_region, skeleton, num_sample_points=20):
        """计算裂缝宽度
        在裂缝区域的上下两侧选取若干个点计算距离
        :param binary_crack_region: 二值化裂缝区域
        :param skeleton: 裂缝骨架
        :param num_sample_points: 采样点数量
        :return: 宽度测量结果
        """
        # 获取骨架点坐标
        skeleton_points = np.column_stack(np.where(skeleton))
        
        if len(skeleton_points) == 0:
            return 0, 0, []
        
        # 均匀采样骨架点
        if len(skeleton_points) > num_sample_points:
            indices = np.linspace(0, len(skeleton_points)-1, num_sample_points, dtype=int)
            sample_points = skeleton_points[indices]
        else:
            sample_points = skeleton_points
        
        width_measurements = []
        
        for point in sample_points:
            y, x = point
            
            # 在垂直方向上寻找裂缝边界
            # 向上搜索
            upper_bound = y
            for i in range(y, max(0, y-50), -1):
                if binary_crack_region[i, x] == 0:
                    upper_bound = i
                    break
            
            # 向下搜索
            lower_bound = y
            for i in range(y, min(binary_crack_region.shape[0], y+50)):
                if binary_crack_region[i, x] == 0:
                    lower_bound = i
                    break
            
            # 计算宽度
            width_pixels = abs(lower_bound - upper_bound)
            if width_pixels > 0:
                width_measurements.append(width_pixels)
        
        if width_measurements:
            avg_width_pixels = np.mean(width_measurements)
            avg_width_actual = avg_width_pixels * self.scale_factor
        else:
            avg_width_pixels = 0
            avg_width_actual = 0
        
        return avg_width_pixels, avg_width_actual, width_measurements

    def calculate_crack_area(self, binary_crack_region):
        """计算裂缝面积
        统计裂缝区域的像素点个数
        :param binary_crack_region: 二值化裂缝区域
        :return: 裂缝面积（像素和实际单位）
        """
        # 统计白色像素（裂缝像素）
        crack_pixels = np.sum(binary_crack_region > 0)
        
        # 转换为实际面积
        actual_area = crack_pixels * (self.scale_factor ** 2)
        
        return crack_pixels, actual_area

    def detect_pavement_cracks(self, image, return_debug=False):
        """完整的路面裂缝检测流程
        :param image: 输入图像
        :param return_debug: 是否返回调试信息
        :return: 检测结果
        """
        debug_info = {} if return_debug else None
        
        try:
            # 1. 图像预处理
            preprocessed, preprocess_steps = self.advanced_preprocess(image)
            if return_debug:
                debug_info['preprocess_steps'] = preprocess_steps
            
            # 2. 裂缝检测 - Canny边缘检测
            canny_edges = self.canny_edge_detection(preprocessed)
            if return_debug:
                debug_info['canny_edges'] = canny_edges
            
            # 3. 裂缝检测 - Gabor滤波器
            gabor_response = self.gabor_filter_detection(preprocessed)
            if return_debug:
                debug_info['gabor_response'] = gabor_response
            
            # 4. 融合检测结果
            combined = cv2.bitwise_or(canny_edges, gabor_response)
            if return_debug:
                debug_info['combined_detection'] = combined
            
            # 5. 二值化
            binary = self.advanced_binarization(combined)
            if return_debug:
                debug_info['binary'] = binary
            
            # 6. 形态学处理
            morphed = self.morphological_processing(binary)
            if return_debug:
                debug_info['morphed'] = morphed
            
            # 7. 裂缝分割
            segmentation_mask, contours = self.crack_segmentation(morphed)
            if return_debug:
                debug_info['segmentation_mask'] = segmentation_mask
                debug_info['contours'] = contours
            
            # 8. 长度计算
            length_pixels, length_actual, skeleton = self.calculate_crack_length(segmentation_mask)
            if return_debug:
                debug_info['skeleton'] = skeleton
            
            # 9. 宽度计算
            width_pixels, width_actual, width_measurements = self.calculate_crack_width(
                segmentation_mask, skeleton
            )
            if return_debug:
                debug_info['width_measurements'] = width_measurements
            
            # 10. 面积计算
            area_pixels, area_actual = self.calculate_crack_area(segmentation_mask)
            
            # 整理结果
            result = {
                'crack_detected': len(contours) > 0,
                'num_cracks': len(contours),
                'length_pixels': length_pixels,
                'length_actual': length_actual,
                'width_pixels': width_pixels,
                'width_actual': width_actual,
                'area_pixels': area_pixels,
                'area_actual': area_actual,
                'contours': contours,
                'segmentation_mask': segmentation_mask,
                'skeleton': skeleton,
                'scale_factor': self.scale_factor,
                'unit': self.unit,
                'width_measurements': width_measurements
            }
            
            if return_debug:
                result['debug_info'] = debug_info
            
            return result
            
        except Exception as e:
            return {
                'error': f"检测过程中出错: {str(e)}",
                'crack_detected': False
            }

    def visualize_results(self, image, result, save_path=None):
        """可视化检测结果
        :param image: 原始图像
        :param result: 检测结果
        :param save_path: 保存路径
        :return: 可视化图像
        """
        if 'error' in result:
            print(f"无法可视化: {result['error']}")
            return image.copy()
        
        vis_image = image.copy()
        
        if result['crack_detected']:
            # 绘制轮廓
            cv2.drawContours(vis_image, result['contours'], -1, (0, 255, 0), 2)
            
            # 绘制骨架
            skeleton_color = cv2.cvtColor(result['skeleton'].astype(np.uint8) * 255, cv2.COLOR_GRAY2BGR)
            skeleton_color[:, :, 0] = 0  # 移除蓝色通道
            skeleton_color[:, :, 1] = 0  # 移除绿色通道
            vis_image = cv2.addWeighted(vis_image, 0.8, skeleton_color, 0.2, 0)
            
            # 添加测量信息
            y_offset = 30
            font = cv2.FONT_HERSHEY_SIMPLEX
            
            cv2.putText(vis_image, f"裂缝数量: {result['num_cracks']}", 
                       (10, y_offset), font, 0.7, (0, 0, 255), 2)
            y_offset += 30
            
            cv2.putText(vis_image, f"长度: {result['length_actual']:.2f} {self.unit}", 
                       (10, y_offset), font, 0.7, (0, 0, 255), 2)
            y_offset += 30
            
            cv2.putText(vis_image, f"平均宽度: {result['width_actual']:.2f} {self.unit}", 
                       (10, y_offset), font, 0.7, (0, 0, 255), 2)
            y_offset += 30
            
            cv2.putText(vis_image, f"面积: {result['area_actual']:.2f} {self.unit}²", 
                       (10, y_offset), font, 0.7, (0, 0, 255), 2)
        else:
            cv2.putText(vis_image, "未检测到裂缝", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)
        
        if save_path:
            os.makedirs(os.path.dirname(save_path) if os.path.dirname(save_path) else '.', exist_ok=True)
            cv2.imwrite(save_path, vis_image)
        
        return vis_image

    def set_parameters(self, **kwargs):
        """设置检测参数
        :param kwargs: 参数字典
        """
        for param_group, params in kwargs.items():
            if hasattr(self, param_group):
                param_dict = getattr(self, param_group)
                param_dict.update(params)
                print(f"已更新 {param_group}: {params}")
            else:
                print(f"警告: 未知参数组 {param_group}")

    def get_parameters(self):
        """获取当前参数设置
        :return: 参数字典
        """
        return {
            'scale_factor': self.scale_factor,
            'unit': self.unit,
            'preprocess_params': self.preprocess_params,
            'canny_params': self.canny_params,
            'gabor_params': self.gabor_params,
            'binarization_params': self.binarization_params,
            'morphology_params': self.morphology_params
        }


# 示例用法
if __name__ == "__main__":
    print("=== 路面裂缝检测专业模块测试 ===")
    
    # 创建检测器
    detector = PavementCrackDetector(scale_factor=0.1, unit='mm')
    
    # 测试图像路径
    test_image_path = "test_images/sample_crack.jpg"
    
    if os.path.exists(test_image_path):
        # 读取图像
        image = cv2.imread(test_image_path)
        
        # 执行检测
        result = detector.detect_pavement_cracks(image, return_debug=True)
        
        if 'error' not in result:
            print(f"检测结果:")
            print(f"  是否检测到裂缝: {result['crack_detected']}")
            print(f"  裂缝数量: {result['num_cracks']}")
            print(f"  长度: {result['length_actual']:.2f} {detector.unit}")
            print(f"  平均宽度: {result['width_actual']:.2f} {detector.unit}")
            print(f"  面积: {result['area_actual']:.2f} {detector.unit}²")
            
            # 可视化结果
            vis_result = detector.visualize_results(
                image, result, 
                save_path="output/pavement_crack_result.jpg"
            )
            
            print("检测完成！结果已保存。")
        else:
            print(f"检测失败: {result['error']}")
    else:
        print(f"测试图像不存在: {test_image_path}")

    def generate_detailed_report(self, results_list, output_path):
        """生成详细的检测报告
        :param results_list: 检测结果列表
        :param output_path: 输出路径
        """
        try:
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    '图像路径', '检测时间', '是否检测到裂缝', '裂缝数量',
                    '长度(像素)', '长度(实际)', '平均宽度(像素)', '平均宽度(实际)',
                    '面积(像素)', '面积(实际)', '比例尺', '单位', '检测方法'
                ]

                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for result in results_list:
                    if 'error' in result:
                        writer.writerow({
                            '图像路径': result.get('image_path', ''),
                            '检测时间': result.get('timestamp', ''),
                            '检测方法': '路面裂缝专业检测',
                            '是否检测到裂缝': '错误: ' + result['error']
                        })
                        continue

                    writer.writerow({
                        '图像路径': result.get('image_path', ''),
                        '检测时间': result.get('timestamp', datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
                        '是否检测到裂缝': '是' if result['crack_detected'] else '否',
                        '裂缝数量': result['num_cracks'],
                        '长度(像素)': result['length_pixels'],
                        '长度(实际)': f"{result['length_actual']:.2f}",
                        '平均宽度(像素)': f"{result['width_pixels']:.2f}",
                        '平均宽度(实际)': f"{result['width_actual']:.2f}",
                        '面积(像素)': result['area_pixels'],
                        '面积(实际)': f"{result['area_actual']:.2f}",
                        '比例尺': self.scale_factor,
                        '单位': self.unit,
                        '检测方法': '路面裂缝专业检测(Canny+Gabor)'
                    })

            print(f"详细报告已生成: {output_path}")
            return True

        except Exception as e:
            print(f"报告生成失败: {e}")
            return False

    def batch_process_images(self, image_paths, output_dir, generate_report=True):
        """批量处理图像
        :param image_paths: 图像路径列表或文件夹路径
        :param output_dir: 输出目录
        :param generate_report: 是否生成报告
        :return: 处理结果列表
        """
        from pathlib import Path

        # 处理输入路径
        if isinstance(image_paths, str):
            folder_path = Path(image_paths)
            if folder_path.is_dir():
                image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
                image_paths = []
                for ext in image_extensions:
                    image_paths.extend(list(folder_path.glob(f'*{ext}')))
                    image_paths.extend(list(folder_path.glob(f'*{ext.upper()}')))
                image_paths = [str(p) for p in image_paths]
            else:
                image_paths = [image_paths]

        if not image_paths:
            print("未找到图像文件")
            return []

        print(f"开始批量处理 {len(image_paths)} 张图像...")

        results = []
        successful_count = 0
        failed_count = 0

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        for i, image_path in enumerate(image_paths):
            print(f"处理图像 {i+1}/{len(image_paths)}: {os.path.basename(image_path)}")

            try:
                # 读取图像
                image = cv2.imread(image_path)
                if image is None:
                    result = {
                        'error': f"无法读取图像: {image_path}",
                        'image_path': image_path,
                        'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    results.append(result)
                    failed_count += 1
                    continue

                # 执行检测
                result = self.detect_pavement_cracks(image, return_debug=False)
                result['image_path'] = image_path
                result['timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # 保存可视化结果
                if 'error' not in result:
                    filename = os.path.basename(image_path)
                    base_name = os.path.splitext(filename)[0]
                    vis_path = os.path.join(output_dir, f"{base_name}_pavement_result.jpg")

                    self.visualize_results(image, result, vis_path)
                    result['visualization_path'] = vis_path

                    successful_count += 1
                    print(f"  成功: 检测到 {result['num_cracks']} 个裂缝, "
                          f"总长度 {result['length_actual']:.2f}{self.unit}")
                else:
                    failed_count += 1
                    print(f"  失败: {result['error']}")

                results.append(result)

            except Exception as e:
                result = {
                    'error': f"处理过程中出错: {str(e)}",
                    'image_path': image_path,
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                results.append(result)
                failed_count += 1
                print(f"  失败: {str(e)}")

        # 生成报告
        if generate_report:
            report_path = os.path.join(output_dir, f"pavement_crack_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
            self.generate_detailed_report(results, report_path)

        # 打印总结
        print(f"\n批量处理完成:")
        print(f"  成功: {successful_count} 张")
        print(f"  失败: {failed_count} 张")
        print(f"  总计: {len(image_paths)} 张")

        return results

    def create_debug_visualization(self, image, result, save_dir):
        """创建调试可视化图像
        :param image: 原始图像
        :param result: 检测结果（包含debug_info）
        :param save_dir: 保存目录
        """
        if 'debug_info' not in result:
            print("没有调试信息可显示")
            return

        debug_info = result['debug_info']
        os.makedirs(save_dir, exist_ok=True)

        # 保存预处理步骤
        if 'preprocess_steps' in debug_info:
            for step_name, step_image in debug_info['preprocess_steps'].items():
                save_path = os.path.join(save_dir, f"{step_name}.jpg")
                cv2.imwrite(save_path, step_image)

        # 保存检测步骤
        detection_steps = ['canny_edges', 'gabor_response', 'combined_detection',
                          'binary', 'morphed', 'segmentation_mask']

        for step_name in detection_steps:
            if step_name in debug_info:
                save_path = os.path.join(save_dir, f"{step_name}.jpg")
                cv2.imwrite(save_path, debug_info[step_name])

        # 保存骨架
        if 'skeleton' in debug_info:
            skeleton_image = debug_info['skeleton'].astype(np.uint8) * 255
            save_path = os.path.join(save_dir, "skeleton.jpg")
            cv2.imwrite(save_path, skeleton_image)

        print(f"调试图像已保存到: {save_dir}")

    def compare_detection_methods(self, image):
        """比较不同检测方法的效果
        :param image: 输入图像
        :return: 比较结果
        """
        results = {}

        # 仅Canny检测
        preprocessed, _ = self.advanced_preprocess(image)
        canny_only = self.canny_edge_detection(preprocessed)
        binary_canny = self.advanced_binarization(canny_only)
        morphed_canny = self.morphological_processing(binary_canny)
        mask_canny, contours_canny = self.crack_segmentation(morphed_canny)

        results['canny_only'] = {
            'mask': mask_canny,
            'contours': contours_canny,
            'num_cracks': len(contours_canny)
        }

        # 仅Gabor检测
        gabor_only = self.gabor_filter_detection(preprocessed)
        binary_gabor = self.advanced_binarization(gabor_only)
        morphed_gabor = self.morphological_processing(binary_gabor)
        mask_gabor, contours_gabor = self.crack_segmentation(morphed_gabor)

        results['gabor_only'] = {
            'mask': mask_gabor,
            'contours': contours_gabor,
            'num_cracks': len(contours_gabor)
        }

        # 融合检测
        combined = cv2.bitwise_or(canny_only, gabor_only)
        binary_combined = self.advanced_binarization(combined)
        morphed_combined = self.morphological_processing(binary_combined)
        mask_combined, contours_combined = self.crack_segmentation(morphed_combined)

        results['combined'] = {
            'mask': mask_combined,
            'contours': contours_combined,
            'num_cracks': len(contours_combined)
        }

        return results

    def optimize_parameters(self, image, ground_truth_mask=None):
        """参数优化建议
        :param image: 测试图像
        :param ground_truth_mask: 真实标注掩码（可选）
        :return: 优化建议
        """
        suggestions = []

        # 测试当前参数
        result = self.detect_pavement_cracks(image, return_debug=True)

        if 'error' in result:
            suggestions.append("检测失败，建议检查图像质量")
            return suggestions

        # 基于检测结果给出建议
        if not result['crack_detected']:
            suggestions.append("未检测到裂缝，建议:")
            suggestions.append("  - 降低Canny阈值 (当前: {}-{})".format(
                self.canny_params['low_threshold'], self.canny_params['high_threshold']))
            suggestions.append("  - 调整二值化方法或阈值")
            suggestions.append("  - 增加Gabor滤波器频率范围")

        elif result['num_cracks'] > 10:
            suggestions.append("检测到过多裂缝，可能存在噪声，建议:")
            suggestions.append("  - 提高Canny阈值")
            suggestions.append("  - 增加形态学开运算核大小")
            suggestions.append("  - 提高最小面积阈值")

        # 检查宽度测量
        if result['width_actual'] < 0.5:  # 假设最小合理宽度为0.5mm
            suggestions.append("检测到的裂缝宽度过小，建议:")
            suggestions.append("  - 检查比例尺设置")
            suggestions.append("  - 调整宽度计算采样方法")

        if not suggestions:
            suggestions.append("当前参数设置合理，检测结果良好")

        return suggestions
