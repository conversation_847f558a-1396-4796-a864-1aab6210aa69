#!/bin/bash

# 增强版裂缝检测系统 - Linux/macOS安装器
# Enhanced Crack Detection - Linux/macOS Installer

echo ""
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║              增强版裂缝检测系统 - Linux安装器                 ║"
echo "║            Enhanced Crack Detection - Linux Installer        ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""

# 检查Python环境
echo "🔍 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python未安装"
        echo "请先安装Python 3.7或更高版本"
        echo "Ubuntu/Debian: sudo apt-get install python3 python3-pip"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "macOS: brew install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ Python环境检查通过"
echo ""

# 检查Python版本
echo "🔍 检查Python版本..."
PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "当前Python版本: $PYTHON_VERSION"

if $PYTHON_CMD -c "import sys; exit(0 if sys.version_info >= (3, 7) else 1)"; then
    echo "✅ Python版本符合要求"
else
    echo "❌ Python版本过低，需要Python 3.7或更高版本"
    exit 1
fi

echo ""

# 检查pip
echo "🔍 检查pip..."
if ! $PYTHON_CMD -m pip --version &> /dev/null; then
    echo "❌ pip未安装"
    echo "请先安装pip"
    exit 1
fi

echo "✅ pip检查通过"
echo ""

# 升级pip
echo "🔄 升级pip..."
$PYTHON_CMD -m pip install --upgrade pip

echo ""
echo "📦 安装核心依赖包..."
$PYTHON_CMD -m pip install numpy opencv-python matplotlib scikit-image scikit-learn scipy pandas

echo ""
echo "🖼️ 安装GUI界面包..."
$PYTHON_CMD -m pip install PyQt5

echo ""
echo "🤖 安装YOLO深度学习支持 (可选)..."
read -p "是否安装YOLO支持? (y/n, 默认y): " install_yolo
install_yolo=${install_yolo:-y}

if [[ $install_yolo =~ ^[Yy]$ ]]; then
    $PYTHON_CMD -m pip install ultralytics
    echo "✅ YOLO支持已安装"
else
    echo "⚠️ 跳过YOLO支持安装"
fi

echo ""
echo "🧪 创建测试环境..."
mkdir -p test_images output models

echo ""
echo "🔍 验证安装..."
$PYTHON_CMD install_dependencies.py

echo ""
echo "🎉 安装完成！"
echo ""
echo "现在您可以运行以下命令开始使用:"
echo "  python3 quick_start.py          # 交互式启动"
echo "  python3 enhanced_crack_gui.py   # 图形界面"
echo "  python3 enhanced_crack_detect.py --help  # 命令行帮助"
echo ""

# 设置执行权限
chmod +x enhanced_crack_detect.py
chmod +x quick_start.py
chmod +x install_dependencies.py

echo "✅ 文件权限设置完成"
