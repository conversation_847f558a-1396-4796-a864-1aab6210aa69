# -*- coding: utf-8 -*-
"""
增强版裂缝检测GUI界面

功能：
1. 加载图片选择要检测的图像
2. 调整参数（比例尺、最小面积阈值）
3. 点击"开始检测"执行检测
4. 查看结果并保存
5. 实时显示检测结果
6. 参数调节界面
7. 批量处理功能
8. 宽度计算功能
"""

import sys
import os
import cv2
import numpy as np
import json
from datetime import datetime
from pathlib import Path

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QFileDialog, QLineEdit, QMessageBox,
    QProgressBar, QCheckBox, QGroupBox, QSlider, QSpinBox,
    QDoubleSpinBox, QTabWidget, QTextEdit, QSplitter, QFrame,
    QGridLayout, QComboBox, QScrollArea, QProgressDialog
)
from PyQt5.QtGui import QPixmap, QImage, QFont, QIcon
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QSize

# 设置全局字体为微软雅黑
def setup_font():
    """设置全局字体为微软雅黑"""
    font = QFont("Microsoft YaHei", 9)  # 微软雅黑，9号字体
    QApplication.setFont(font)
    return font

# 导入我们的检测模块
from crack_detection import EnhancedCrackDetector
from crack_width_calculator import CrackWidthCalculator
from advanced_segmentation_system import AdvancedSegmentationSystem
from optimal_crack_analysis_system import OptimalCrackAnalysisSystem, AnalysisMode


class ImageProcessingThread(QThread):
    """图像处理线程"""
    finished = pyqtSignal(dict)
    progress = pyqtSignal(int)
    status_update = pyqtSignal(str)

    def __init__(self, detector, image_path, use_yolo=True,
                 use_pavement_detection=False, use_concrete_detection=False,
                 calculate_width=False, optimal_system=None, analysis_mode=None):
        super().__init__()
        self.detector = detector
        self.optimal_system = optimal_system
        self.image_path = image_path
        self.use_yolo = use_yolo
        self.use_pavement_detection = use_pavement_detection
        self.use_concrete_detection = use_concrete_detection
        self.calculate_width = calculate_width
        self.analysis_mode = analysis_mode
        self.width_calculator = None

        if calculate_width:
            self.width_calculator = CrackWidthCalculator(
                scale_factor=detector.scale_factor,
                unit=detector.unit.replace('²', '')
            )
    
    def run(self):
        try:
            self.status_update.emit("开始处理图像...")
            self.progress.emit(10)

            # 如果有最优系统且指定了分析模式，使用最优系统
            if self.optimal_system and self.analysis_mode:
                self.status_update.emit("使用最优分析系统...")

                # 加载图像
                image = cv2.imread(self.image_path)
                if image is None:
                    raise Exception("无法加载图像")

                # 执行最优分析
                result = self.optimal_system.analyze_image(image, self.analysis_mode)

                # 转换结果格式以兼容现有界面
                if result['success']:
                    area_info = result['area_analysis']
                    result['area_info'] = area_info
                    result['processing_time'] = result['analysis_time']
                    result['detection_method'] = f"最优系统-{result['analysis_mode']}"
                    result['image_path'] = self.image_path
                    result['unit'] = area_info['unit']

                    # 生成可视化结果
                    result_image = self._create_visualization_from_optimal_result(image, result)
                    result['result_image'] = result_image

                    # 设置保存路径
                    import os
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    save_dir = "output/gui_results"
                    os.makedirs(save_dir, exist_ok=True)
                    save_path = os.path.join(save_dir, f"optimal_result_{timestamp}.jpg")
                    cv2.imwrite(save_path, result_image)
                    result['save_path'] = save_path

                self.progress.emit(70)
            else:
                # 使用传统检测器
                result = self.detector.process_single_image(
                    self.image_path,
                    output_dir="output/gui_results",
                    use_yolo=self.use_yolo,
                    use_pavement_detection=self.use_pavement_detection,
                    use_concrete_detection=self.use_concrete_detection
                )

                self.progress.emit(70)
            
            # 如果需要计算宽度
            if self.calculate_width and 'error' not in result:
                self.status_update.emit("计算裂缝宽度...")

                # 获取掩码用于宽度计算
                mask = None
                image = cv2.imread(self.image_path)

                # 从不同类型的结果中获取掩码
                if 'contours' in result and result['contours']:
                    # 传统检测器结果
                    contours = result['contours']
                    mask = np.zeros(image.shape[:2], dtype=np.uint8)
                    cv2.fillPoly(mask, contours, 255)
                elif self.optimal_system and 'segmentation_info' in result:
                    # 最优系统结果 - 重新获取掩码
                    mask, _ = self.optimal_system.segmentation_system.segment_cracks(image, 0.25)

                if mask is not None:
                    # 使用传统宽度计算器
                    if self.width_calculator:
                        width_result = self.width_calculator.calculate_crack_width(mask, return_debug=True)
                        result['width_info'] = width_result

                        # 可视化宽度结果
                        from datetime import datetime
                        width_vis = self.width_calculator.visualize_width_results(
                            image, width_result,
                            save_path=f"output/gui_results/width_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
                        )
                        result['width_visualization'] = width_vis

                    # 如果最优系统已经有宽度分析，合并结果
                    if 'width_analysis' in result and result['width_analysis']:
                        optimal_width = result['width_analysis']
                        if 'width_statistics' in optimal_width:
                            # 将最优系统的宽度分析添加到结果中
                            result['optimal_width_analysis'] = optimal_width
                else:
                    print("⚠️  无法获取掩码进行宽度计算")
            
            self.progress.emit(100)
            self.status_update.emit("处理完成")
            self.finished.emit(result)
            
        except Exception as e:
            error_result = {
                'error': f"处理失败: {str(e)}",
                'image_path': self.image_path
            }
            self.finished.emit(error_result)

    def _create_visualization_from_optimal_result(self, image, result):
        """从最优分析结果创建可视化图像"""
        try:
            # 创建结果图像副本
            result_image = image.copy()

            # 如果有分割信息，重新生成掩码进行可视化
            if 'segmentation_info' in result and 'area_analysis' in result:
                # 尝试从最优系统重新获取掩码
                if hasattr(self.optimal_system, 'segmentation_system'):
                    mask, _ = self.optimal_system.segmentation_system.segment_cracks(image, 0.25)
                    if mask is not None:
                        # 在原图上绘制检测结果
                        result_image = self._draw_detection_results(result_image, mask, result)
                    else:
                        # 如果无法获取掩码，添加文本信息
                        result_image = self._add_text_overlay(result_image, result)
                else:
                    # 添加文本信息
                    result_image = self._add_text_overlay(result_image, result)
            else:
                # 添加文本信息
                result_image = self._add_text_overlay(result_image, result)

            return result_image

        except Exception as e:
            print(f"可视化生成失败: {e}")
            # 返回带有错误信息的原图
            result_image = image.copy()
            cv2.putText(result_image, f"Visualization Error: {str(e)}",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            return result_image

    def _draw_detection_results(self, image, mask, result):
        """在图像上绘制检测结果"""
        try:
            from crack_detection import put_chinese_text

            # 找到轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 绘制轮廓
            cv2.drawContours(image, contours, -1, (0, 255, 0), 2)

            # 填充检测区域（半透明）
            overlay = image.copy()
            cv2.fillPoly(overlay, contours, (0, 255, 0))
            image = cv2.addWeighted(image, 0.8, overlay, 0.2, 0)

            # 添加标注信息
            area_info = result['area_analysis']
            y_offset = 30

            # 使用中文字体显示信息
            image = put_chinese_text(image, f"检测方法: {result['detection_method']}",
                                   (10, y_offset), 18, (255, 255, 255))
            y_offset += 30

            image = put_chinese_text(image, f"裂缝数量: {area_info['contour_count']}",
                                   (10, y_offset), 18, (255, 255, 255))
            y_offset += 30

            image = put_chinese_text(image, f"总面积: {area_info['total_real_area']:.2f} {area_info['unit']}",
                                   (10, y_offset), 18, (255, 255, 255))
            y_offset += 30

            if area_info['contour_count'] > 0:
                image = put_chinese_text(image, f"平均面积: {area_info['area_statistics']['mean_area']:.2f} {area_info['unit']}",
                                       (10, y_offset), 16, (255, 255, 255))

            return image

        except Exception as e:
            print(f"绘制检测结果失败: {e}")
            return self._add_text_overlay(image, result)

    def _add_text_overlay(self, image, result):
        """添加文本覆盖信息"""
        try:
            from crack_detection import put_chinese_text

            area_info = result['area_analysis']
            y_offset = 30

            # 添加基本信息
            image = put_chinese_text(image, f"分析模式: {result['analysis_mode']}",
                                   (10, y_offset), 18, (0, 255, 0))
            y_offset += 30

            image = put_chinese_text(image, f"裂缝数量: {area_info['contour_count']}",
                                   (10, y_offset), 18, (0, 255, 0))
            y_offset += 30

            image = put_chinese_text(image, f"总面积: {area_info['total_real_area']:.2f} {area_info['unit']}",
                                   (10, y_offset), 18, (0, 255, 0))
            y_offset += 30

            image = put_chinese_text(image, f"处理时间: {result['processing_time']:.2f}秒",
                                   (10, y_offset), 16, (0, 255, 0))

            # 如果有质量评估信息
            if 'quality_assessment' in result and result['quality_assessment']:
                quality = result['quality_assessment']
                y_offset += 30
                image = put_chinese_text(image, f"质量评分: {quality['quality_score']:.2f}",
                                       (10, y_offset), 16, (0, 255, 0))

            return image

        except Exception as e:
            print(f"添加文本覆盖失败: {e}")
            # 最基本的文本显示
            cv2.putText(image, f"Analysis Complete", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(image, f"Cracks: {result['area_analysis']['contour_count']}", (10, 70),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            return image


class BatchProcessingThread(QThread):
    """批量处理线程"""
    finished = pyqtSignal(list)
    progress = pyqtSignal(int)
    status_update = pyqtSignal(str)
    
    def __init__(self, detector, folder_path, use_yolo=True):
        super().__init__()
        self.detector = detector
        self.folder_path = folder_path
        self.use_yolo = use_yolo
    
    def run(self):
        try:
            def progress_callback(progress):
                self.progress.emit(progress)
                self.status_update.emit(f"批量处理进度: {progress}%")
            
            results = self.detector.batch_process_images(
                self.folder_path,
                output_dir="output/batch_results",
                use_yolo=self.use_yolo,
                generate_csv=True,
                progress_callback=progress_callback
            )
            
            self.finished.emit(results)
            
        except Exception as e:
            self.status_update.emit(f"批量处理失败: {str(e)}")
            self.finished.emit([])


class EnhancedCrackDetectionGUI(QMainWindow):
    """增强版裂缝检测GUI主界面"""
    
    def __init__(self):
        super().__init__()

        # 设置全局字体
        self.font = setup_font()

        self.detector = None
        self.optimal_system = None
        self.current_image_path = None
        self.current_result = None
        self.processing_thread = None
        self.batch_thread = None

        self.init_ui()
        self.load_custom_model_config()
        self.init_detector()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("增强版裂缝检测系统 v2.0")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建主分割器
        self.main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(self.main_splitter)

        # 配置分割器属性
        self.setup_splitter()

        # 左侧控制面板
        self.create_control_panel(self.main_splitter)

        # 右侧显示区域
        self.create_display_area(self.main_splitter)

        # 设置分割器初始比例和约束
        self.configure_splitter_layout()
        
        # 状态栏
        self.statusBar().showMessage("就绪")
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)

        # 设置键盘快捷键
        self.setup_shortcuts()

        # 加载保存的布局状态
        self.load_splitter_state()
    
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setMaximumWidth(400)
        
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # 1. 图像选择组
        self.create_image_selection_group(scroll_layout)
        
        # 2. 参数设置组
        self.create_parameter_group(scroll_layout)
        
        # 3. 检测选项组
        self.create_detection_options_group(scroll_layout)
        
        # 4. 操作按钮组
        self.create_action_buttons_group(scroll_layout)

        # 5. 界面布局控制组
        self.create_layout_control_group(scroll_layout)

        # 6. 结果显示组
        self.create_results_display_group(scroll_layout)
        
        scroll_layout.addStretch()
        scroll.setWidget(scroll_content)
        control_layout.addWidget(scroll)
        
        parent.addWidget(control_widget)
    
    def create_image_selection_group(self, layout):
        """创建图像选择组"""
        group = QGroupBox("图像选择")
        group_layout = QVBoxLayout(group)
        
        # 单张图片选择
        single_layout = QHBoxLayout()
        self.image_path_edit = QLineEdit()
        self.image_path_edit.setPlaceholderText("选择要检测的图像文件")
        self.image_path_edit.setReadOnly(True)
        
        browse_btn = QPushButton("浏览图片")
        browse_btn.clicked.connect(self.browse_image)
        
        single_layout.addWidget(self.image_path_edit)
        single_layout.addWidget(browse_btn)
        group_layout.addLayout(single_layout)
        
        # 文件夹选择
        folder_layout = QHBoxLayout()
        self.folder_path_edit = QLineEdit()
        self.folder_path_edit.setPlaceholderText("选择图片文件夹（批量处理）")
        self.folder_path_edit.setReadOnly(True)
        
        browse_folder_btn = QPushButton("浏览文件夹")
        browse_folder_btn.clicked.connect(self.browse_folder)
        
        folder_layout.addWidget(self.folder_path_edit)
        folder_layout.addWidget(browse_folder_btn)
        group_layout.addLayout(folder_layout)
        
        layout.addWidget(group)
    
    def create_parameter_group(self, layout):
        """创建参数设置组"""
        group = QGroupBox("参数设置")
        group_layout = QGridLayout(group)
        
        # 分析模式选择
        group_layout.addWidget(QLabel("分析模式:"), 0, 0)
        self.analysis_mode_combo = QComboBox()
        self.analysis_mode_combo.addItems(["快速模式", "平衡模式", "精确模式", "研究模式"])
        self.analysis_mode_combo.setCurrentText("平衡模式")
        self.analysis_mode_combo.setToolTip("选择分析精度和速度的平衡")
        group_layout.addWidget(self.analysis_mode_combo, 0, 1)

        # 比例尺设置
        group_layout.addWidget(QLabel("比例尺:"), 1, 0)
        self.scale_factor_spin = QDoubleSpinBox()
        self.scale_factor_spin.setRange(0.001, 10.0)
        self.scale_factor_spin.setValue(0.1)
        self.scale_factor_spin.setDecimals(3)
        self.scale_factor_spin.setSuffix(" mm/像素")
        group_layout.addWidget(self.scale_factor_spin, 1, 1)
        
        # 面积单位
        group_layout.addWidget(QLabel("面积单位:"), 2, 0)
        self.unit_combo = QComboBox()
        self.unit_combo.addItems(["mm²", "cm²", "m²", "像素²"])
        group_layout.addWidget(self.unit_combo, 2, 1)

        # 最小面积阈值
        group_layout.addWidget(QLabel("最小面积:"), 3, 0)
        self.min_area_spin = QSpinBox()
        self.min_area_spin.setRange(10, 10000)
        self.min_area_spin.setValue(50)
        self.min_area_spin.setSuffix(" 像素²")
        group_layout.addWidget(self.min_area_spin, 3, 1)
        
        # 最大面积阈值
        group_layout.addWidget(QLabel("最大面积:"), 3, 0)
        self.max_area_spin = QSpinBox()
        self.max_area_spin.setRange(100, 100000)
        self.max_area_spin.setValue(50000)
        self.max_area_spin.setSuffix(" 像素²")
        group_layout.addWidget(self.max_area_spin, 3, 1)
        
        # 圆形度范围
        group_layout.addWidget(QLabel("圆形度范围:"), 4, 0)
        circularity_layout = QHBoxLayout()
        self.min_circularity_spin = QDoubleSpinBox()
        self.min_circularity_spin.setRange(0.0, 1.0)
        self.min_circularity_spin.setValue(0.1)
        self.min_circularity_spin.setDecimals(2)
        
        self.max_circularity_spin = QDoubleSpinBox()
        self.max_circularity_spin.setRange(0.0, 1.0)
        self.max_circularity_spin.setValue(0.9)
        self.max_circularity_spin.setDecimals(2)
        
        circularity_layout.addWidget(self.min_circularity_spin)
        circularity_layout.addWidget(QLabel("-"))
        circularity_layout.addWidget(self.max_circularity_spin)
        group_layout.addLayout(circularity_layout, 4, 1)
        
        # 应用参数按钮
        apply_btn = QPushButton("应用参数")
        apply_btn.clicked.connect(self.apply_parameters)
        group_layout.addWidget(apply_btn, 5, 0, 1, 2)
        
        layout.addWidget(group)
    
    def create_detection_options_group(self, layout):
        """创建检测选项组"""
        group = QGroupBox("检测选项")
        group_layout = QVBoxLayout(group)

        # 检测方法选择
        method_layout = QHBoxLayout()
        method_layout.addWidget(QLabel("检测方法:"))
        self.detection_method_combo = QComboBox()
        self.detection_method_combo.addItems([
            "标准检测", "专业路面检测", "专业混凝土检测", "自动选择"
        ])
        method_layout.addWidget(self.detection_method_combo)
        group_layout.addLayout(method_layout)

        # YOLO选项
        self.use_yolo_cb = QCheckBox("使用YOLO分割模型")
        self.use_yolo_cb.setChecked(True)
        group_layout.addWidget(self.use_yolo_cb)

        # 分割模型选择组
        model_group = QGroupBox("🤖 分割模型选择")
        model_group_layout = QVBoxLayout(model_group)

        # 模型选择主行
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("当前模型:"))
        self.model_combo = QComboBox()
        self.model_combo.setMinimumWidth(250)
        self.model_combo.setToolTip("选择用于裂缝分割的YOLO模型")
        self.model_combo.currentTextChanged.connect(self.on_model_selection_changed)
        model_layout.addWidget(self.model_combo)

        # 模型管理按钮
        self.refresh_models_btn = QPushButton("🔄 刷新")
        self.refresh_models_btn.clicked.connect(self.refresh_models)
        self.refresh_models_btn.setToolTip("刷新可用模型列表")
        model_layout.addWidget(self.refresh_models_btn)

        self.download_model_btn = QPushButton("⬇️ 下载")
        self.download_model_btn.clicked.connect(self.download_selected_model)
        self.download_model_btn.setToolTip("下载选中的模型")
        model_layout.addWidget(self.download_model_btn)

        self.model_info_btn = QPushButton("ℹ️ 信息")
        self.model_info_btn.clicked.connect(self.show_model_info)
        self.model_info_btn.setToolTip("显示模型详细信息")
        model_layout.addWidget(self.model_info_btn)

        model_group_layout.addLayout(model_layout)

        # 模型信息显示
        self.model_info_label = QLabel("模型信息: 未选择")
        self.model_info_label.setStyleSheet("color: #666; font-size: 11px;")
        self.model_info_label.setWordWrap(True)
        model_group_layout.addWidget(self.model_info_label)

        # 智能推荐按钮
        recommend_layout = QHBoxLayout()
        self.recommend_fast_btn = QPushButton("⚡ 推荐快速模型")
        self.recommend_fast_btn.clicked.connect(lambda: self.recommend_model_for_mode("fast"))
        self.recommend_fast_btn.setToolTip("推荐速度优先的轻量级模型")
        recommend_layout.addWidget(self.recommend_fast_btn)

        self.recommend_balanced_btn = QPushButton("⚖️ 推荐平衡模型")
        self.recommend_balanced_btn.clicked.connect(lambda: self.recommend_model_for_mode("balanced"))
        self.recommend_balanced_btn.setToolTip("推荐速度和精度平衡的模型")
        recommend_layout.addWidget(self.recommend_balanced_btn)

        self.recommend_accurate_btn = QPushButton("🎯 推荐精确模型")
        self.recommend_accurate_btn.clicked.connect(lambda: self.recommend_model_for_mode("accurate"))
        self.recommend_accurate_btn.setToolTip("推荐精度优先的高性能模型")
        recommend_layout.addWidget(self.recommend_accurate_btn)

        model_group_layout.addLayout(recommend_layout)

        # 自定义模型选择
        custom_model_group = QGroupBox("📁 自定义模型")
        custom_model_layout = QVBoxLayout(custom_model_group)

        # 自定义模型路径
        custom_path_layout = QHBoxLayout()
        custom_path_layout.addWidget(QLabel("模型路径:"))
        self.custom_model_path_edit = QLineEdit()
        self.custom_model_path_edit.setPlaceholderText("选择自定义YOLO分割模型文件 (.pt)")
        self.custom_model_path_edit.setToolTip("支持.pt格式的YOLO分割模型")
        custom_path_layout.addWidget(self.custom_model_path_edit)

        self.browse_custom_model_btn = QPushButton("📂 浏览")
        self.browse_custom_model_btn.clicked.connect(self.browse_custom_model)
        self.browse_custom_model_btn.setToolTip("浏览选择自定义模型文件")
        custom_path_layout.addWidget(self.browse_custom_model_btn)

        custom_model_layout.addLayout(custom_path_layout)

        # 自定义模型操作按钮
        custom_actions_layout = QHBoxLayout()

        self.load_custom_model_btn = QPushButton("🔄 加载自定义模型")
        self.load_custom_model_btn.clicked.connect(self.load_custom_model)
        self.load_custom_model_btn.setToolTip("加载指定路径的自定义模型")
        custom_actions_layout.addWidget(self.load_custom_model_btn)

        self.validate_custom_model_btn = QPushButton("✅ 验证模型")
        self.validate_custom_model_btn.clicked.connect(self.validate_custom_model)
        self.validate_custom_model_btn.setToolTip("验证模型是否为有效的YOLO分割模型")
        custom_actions_layout.addWidget(self.validate_custom_model_btn)

        self.save_custom_model_btn = QPushButton("💾 保存配置")
        self.save_custom_model_btn.clicked.connect(self.save_custom_model_config)
        self.save_custom_model_btn.setToolTip("保存自定义模型配置以便下次使用")
        custom_actions_layout.addWidget(self.save_custom_model_btn)

        custom_model_layout.addLayout(custom_actions_layout)

        # 自定义模型信息显示
        self.custom_model_info_label = QLabel("自定义模型: 未加载")
        self.custom_model_info_label.setStyleSheet("color: #666; font-size: 11px;")
        self.custom_model_info_label.setWordWrap(True)
        custom_model_layout.addWidget(self.custom_model_info_label)

        # 预设自定义模型
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel("快速选择:"))

        self.preset_custom_combo = QComboBox()
        self.preset_custom_combo.addItems([
            "选择预设模型...",
            "YOLOv8n-seg (轻量级)",
            "YOLOv8s-seg (小型)",
            "YOLOv8m-seg (中型)",
            "YOLOv8l-seg (大型)",
            "YOLOv8x-seg (超大型)",
            "自定义训练模型"
        ])
        self.preset_custom_combo.currentTextChanged.connect(self.on_preset_custom_changed)
        self.preset_custom_combo.setToolTip("选择预设的自定义模型类型")
        preset_layout.addWidget(self.preset_custom_combo)

        custom_model_layout.addLayout(preset_layout)

        model_group_layout.addWidget(custom_model_group)

        group_layout.addWidget(model_group)

        # 初始化模型列表
        self.refresh_models()

        # 专业检测选项
        self.use_pavement_cb = QCheckBox("启用专业路面检测 (Canny + Gabor)")
        self.use_pavement_cb.setChecked(False)
        group_layout.addWidget(self.use_pavement_cb)

        self.use_concrete_cb = QCheckBox("启用专业混凝土检测 (完整长度/宽度/面积)")
        self.use_concrete_cb.setChecked(False)
        group_layout.addWidget(self.use_concrete_cb)

        # 宽度分析选项组
        width_group = QGroupBox("📏 宽度分析选项")
        width_group_layout = QVBoxLayout(width_group)

        # 启用宽度计算
        self.calculate_width_cb = QCheckBox("启用宽度分析")
        self.calculate_width_cb.setChecked(True)  # 默认启用
        self.calculate_width_cb.setToolTip("计算裂缝的宽度分布和统计信息")
        width_group_layout.addWidget(self.calculate_width_cb)

        # 宽度分析方法选择
        width_method_layout = QHBoxLayout()
        width_method_layout.addWidget(QLabel("分析方法:"))
        self.width_method_combo = QComboBox()
        self.width_method_combo.addItems([
            "传统骨架化方法",
            "最优系统方法",
            "双重分析 (推荐)"
        ])
        self.width_method_combo.setCurrentText("双重分析 (推荐)")
        self.width_method_combo.setToolTip("选择宽度分析的计算方法")
        width_method_layout.addWidget(self.width_method_combo)
        width_group_layout.addLayout(width_method_layout)

        # 宽度分析精度
        precision_layout = QHBoxLayout()
        precision_layout.addWidget(QLabel("分析精度:"))
        self.width_precision_combo = QComboBox()
        self.width_precision_combo.addItems([
            "标准精度",
            "高精度",
            "超高精度"
        ])
        self.width_precision_combo.setCurrentText("高精度")
        self.width_precision_combo.setToolTip("更高精度需要更多计算时间")
        precision_layout.addWidget(self.width_precision_combo)
        width_group_layout.addLayout(precision_layout)

        group_layout.addWidget(width_group)

        # 保存结果选项
        self.save_results_cb = QCheckBox("保存检测结果")
        self.save_results_cb.setChecked(True)
        group_layout.addWidget(self.save_results_cb)

        # 显示调试图像选项
        self.show_debug_cb = QCheckBox("显示调试图像")
        self.show_debug_cb.setChecked(False)
        group_layout.addWidget(self.show_debug_cb)

        # 连接信号
        self.detection_method_combo.currentTextChanged.connect(self.on_detection_method_changed)

        layout.addWidget(group)

    def on_detection_method_changed(self, method_text):
        """检测方法改变时的处理"""
        if method_text == "专业路面检测":
            self.use_pavement_cb.setChecked(True)
            self.use_concrete_cb.setChecked(False)
        elif method_text == "专业混凝土检测":
            self.use_pavement_cb.setChecked(False)
            self.use_concrete_cb.setChecked(True)
        elif method_text == "自动选择":
            self.use_pavement_cb.setChecked(True)
            self.use_concrete_cb.setChecked(True)
        else:  # 标准检测
            self.use_pavement_cb.setChecked(False)
            self.use_concrete_cb.setChecked(False)

    def refresh_models(self):
        """刷新模型列表"""
        self.model_combo.clear()

        try:
            from model_manager import model_manager

            # 添加自动选择选项
            self.model_combo.addItem("自动选择", "auto")

            # 获取可用模型
            models = model_manager.get_available_models()
            installed = model_manager.get_installed_models()

            for model_id, config in models.items():
                status = "✅" if model_id in installed else "❌"
                display_name = f"{status} {config['name']} ({config['size_mb']}MB)"
                self.model_combo.addItem(display_name, model_id)

            # 设置默认选择
            recommended = model_manager.recommend_model("balanced")
            for i in range(self.model_combo.count()):
                if self.model_combo.itemData(i) == recommended:
                    self.model_combo.setCurrentIndex(i)
                    break

        except ImportError:
            self.model_combo.addItem("模型管理器不可用", None)
            self.download_model_btn.setEnabled(False)
            self.model_info_label.setText("模型信息: 模型管理器不可用")

        # 更新模型信息显示
        self.update_model_info_display()

    def download_selected_model(self):
        """下载选中的模型"""
        model_id = self.model_combo.currentData()
        if not model_id or model_id == "auto":
            QMessageBox.information(self, "提示", "请选择要下载的模型")
            return

        try:
            from model_manager import model_manager

            if model_manager.is_model_installed(model_id):
                QMessageBox.information(self, "提示", "模型已安装")
                return

            # 显示下载对话框
            reply = QMessageBox.question(
                self, "确认下载",
                f"确定要下载模型 {model_id} 吗？\n这可能需要一些时间。",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 创建进度对话框
                progress = QProgressDialog("正在下载模型...", "取消", 0, 0, self)
                progress.setWindowModality(Qt.WindowModal)
                progress.show()

                # 在后台下载
                success = model_manager.download_model(model_id)
                progress.close()

                if success:
                    QMessageBox.information(self, "成功", f"模型 {model_id} 下载完成！")
                    self.refresh_models()
                    # 重新初始化检测器
                    self.init_detector()
                else:
                    QMessageBox.warning(self, "失败", f"模型 {model_id} 下载失败")

        except ImportError:
            QMessageBox.warning(self, "错误", "模型管理器不可用")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"下载失败: {str(e)}")

    def on_model_selection_changed(self):
        """模型选择改变时的回调"""
        self.update_model_info_display()

    def update_model_info_display(self):
        """更新模型信息显示"""
        try:
            selected_model = self.model_combo.currentData()
            if not selected_model or selected_model == "auto":
                self.model_info_label.setText("模型信息: 自动选择 - 系统将根据图像特征自动选择最佳模型")
                return

            from model_manager import model_manager
            models = model_manager.get_available_models()
            installed = model_manager.get_installed_models()

            if selected_model in models:
                config = models[selected_model]
                status = "已安装" if selected_model in installed else "未安装"

                info_text = f"模型信息: {config['name']} | {config['size_mb']}MB | {status}"
                if 'description' in config:
                    info_text += f" | {config['description']}"

                self.model_info_label.setText(info_text)

                # 根据安装状态启用/禁用下载按钮
                self.download_model_btn.setEnabled(selected_model not in installed)
            else:
                self.model_info_label.setText("模型信息: 未知模型")

        except Exception as e:
            self.model_info_label.setText(f"模型信息: 获取失败 - {str(e)}")

    def show_model_info(self):
        """显示详细的模型信息"""
        try:
            selected_model = self.model_combo.currentData()
            if not selected_model or selected_model == "auto":
                QMessageBox.information(self, "模型信息",
                    "自动选择模式\n\n"
                    "系统将根据以下因素自动选择最佳模型:\n"
                    "• 图像分辨率和复杂度\n"
                    "• 边缘密度和纹理特征\n"
                    "• 可用的计算资源\n"
                    "• 用户选择的分析模式\n\n"
                    "推荐用于大多数场景。")
                return

            from model_manager import model_manager
            models = model_manager.get_available_models()
            installed = model_manager.get_installed_models()

            if selected_model in models:
                config = models[selected_model]
                status = "✅ 已安装" if selected_model in installed else "❌ 未安装"

                info_text = f"模型详细信息\n\n"
                info_text += f"名称: {config['name']}\n"
                info_text += f"模型ID: {selected_model}\n"
                info_text += f"大小: {config['size_mb']} MB\n"
                info_text += f"状态: {status}\n"

                if 'description' in config:
                    info_text += f"描述: {config['description']}\n"

                if 'performance' in config:
                    perf = config['performance']
                    info_text += f"\n性能指标:\n"
                    info_text += f"• 速度: {perf.get('speed', '未知')}\n"
                    info_text += f"• 精度: {perf.get('accuracy', '未知')}\n"
                    info_text += f"• 内存使用: {perf.get('memory', '未知')}\n"

                if 'recommended_for' in config:
                    info_text += f"\n推荐用途:\n"
                    for use_case in config['recommended_for']:
                        info_text += f"• {use_case}\n"

                QMessageBox.information(self, "模型信息", info_text)
            else:
                QMessageBox.warning(self, "错误", "无法获取模型信息")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取模型信息失败: {str(e)}")

    def recommend_model_for_mode(self, mode):
        """为特定模式推荐模型"""
        try:
            from model_manager import model_manager

            recommended = model_manager.recommend_model(mode)

            # 在下拉框中选择推荐的模型
            for i in range(self.model_combo.count()):
                if self.model_combo.itemData(i) == recommended:
                    self.model_combo.setCurrentIndex(i)
                    break

            # 显示推荐信息
            mode_names = {
                "fast": "快速模式",
                "balanced": "平衡模式",
                "accurate": "精确模式"
            }

            QMessageBox.information(self, "模型推荐",
                f"已为{mode_names.get(mode, mode)}推荐模型: {recommended}\n\n"
                f"该模型在{mode_names.get(mode, mode)}下具有最佳性能。")

            self.statusBar().showMessage(f"已推荐{mode_names.get(mode, mode)}模型: {recommended}")

        except Exception as e:
            QMessageBox.warning(self, "推荐失败", f"无法推荐模型: {str(e)}")

    def browse_custom_model(self):
        """浏览选择自定义模型文件"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self,
            "选择自定义YOLO分割模型",
            "",
            "YOLO模型文件 (*.pt);;所有文件 (*.*)"
        )

        if file_path:
            self.custom_model_path_edit.setText(file_path)
            self.custom_model_info_label.setText(f"选择的模型: {os.path.basename(file_path)}")

            # 自动验证模型
            self.validate_custom_model()

    def validate_custom_model(self):
        """验证自定义模型"""
        model_path = self.custom_model_path_edit.text().strip()

        if not model_path:
            QMessageBox.warning(self, "警告", "请先选择模型文件")
            return False

        if not os.path.exists(model_path):
            QMessageBox.warning(self, "错误", "模型文件不存在")
            self.custom_model_info_label.setText("自定义模型: 文件不存在")
            return False

        try:
            # 尝试加载模型进行验证
            from ultralytics import YOLO

            # 创建进度对话框
            progress = QProgressDialog("正在验证模型...", "取消", 0, 0, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            # 验证模型
            model = YOLO(model_path)

            # 检查是否为分割模型
            if hasattr(model.model, 'model') and hasattr(model.model.model[-1], 'nc'):
                # 获取模型信息
                model_info = {
                    'path': model_path,
                    'name': os.path.basename(model_path),
                    'size_mb': os.path.getsize(model_path) / (1024 * 1024),
                    'classes': getattr(model.model.model[-1], 'nc', 'Unknown'),
                    'type': 'segmentation' if 'seg' in model_path.lower() else 'detection'
                }

                progress.close()

                # 显示验证结果
                info_text = f"✅ 模型验证成功\n\n"
                info_text += f"文件名: {model_info['name']}\n"
                info_text += f"大小: {model_info['size_mb']:.1f} MB\n"
                info_text += f"类别数: {model_info['classes']}\n"
                info_text += f"类型: {model_info['type']}\n"

                QMessageBox.information(self, "模型验证", info_text)

                self.custom_model_info_label.setText(
                    f"自定义模型: {model_info['name']} | {model_info['size_mb']:.1f}MB | ✅ 已验证"
                )

                return True
            else:
                progress.close()
                QMessageBox.warning(self, "验证失败", "该模型不是有效的YOLO分割模型")
                self.custom_model_info_label.setText("自定义模型: ❌ 验证失败")
                return False

        except Exception as e:
            if 'progress' in locals():
                progress.close()
            QMessageBox.critical(self, "验证错误", f"模型验证失败: {str(e)}")
            self.custom_model_info_label.setText("自定义模型: ❌ 验证错误")
            return False

    def load_custom_model(self):
        """加载自定义模型"""
        model_path = self.custom_model_path_edit.text().strip()

        if not model_path:
            QMessageBox.warning(self, "警告", "请先选择模型文件")
            return

        # 先验证模型
        if not self.validate_custom_model():
            return

        try:
            # 更新最优系统的分割系统
            if self.optimal_system and hasattr(self.optimal_system, 'segmentation_system'):
                # 加载自定义模型到分割系统
                from ultralytics import YOLO
                custom_model = YOLO(model_path)

                # 更新分割系统的模型
                self.optimal_system.segmentation_system.current_model = custom_model
                self.optimal_system.segmentation_system.current_model_name = f"custom_{os.path.basename(model_path)}"

                # 添加到可用模型列表
                model_name = f"自定义: {os.path.basename(model_path)}"
                self.model_combo.addItem(model_name, f"custom_{model_path}")

                # 选择新加载的模型
                self.model_combo.setCurrentText(model_name)

                # 更新检测器
                self.init_detector()

                QMessageBox.information(self, "成功", f"自定义模型已加载: {os.path.basename(model_path)}")
                self.statusBar().showMessage(f"已加载自定义模型: {os.path.basename(model_path)}")

                # 更新模型信息显示
                self.custom_model_info_label.setText(
                    f"自定义模型: {os.path.basename(model_path)} | ✅ 已加载并激活"
                )

            else:
                QMessageBox.warning(self, "错误", "最优系统未初始化")

        except Exception as e:
            QMessageBox.critical(self, "加载失败", f"无法加载自定义模型: {str(e)}")

    def save_custom_model_config(self):
        """保存自定义模型配置"""
        model_path = self.custom_model_path_edit.text().strip()

        if not model_path:
            QMessageBox.warning(self, "警告", "请先选择模型文件")
            return

        try:
            # 创建配置目录
            config_dir = "config"
            os.makedirs(config_dir, exist_ok=True)

            # 保存配置
            config = {
                'custom_model_path': model_path,
                'model_name': os.path.basename(model_path),
                'saved_time': datetime.now().isoformat(),
                'model_size_mb': os.path.getsize(model_path) / (1024 * 1024) if os.path.exists(model_path) else 0
            }

            config_file = os.path.join(config_dir, "custom_model_config.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            QMessageBox.information(self, "保存成功", f"自定义模型配置已保存到: {config_file}")
            self.statusBar().showMessage("自定义模型配置已保存")

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"无法保存配置: {str(e)}")

    def on_preset_custom_changed(self):
        """预设自定义模型选择改变"""
        selected = self.preset_custom_combo.currentText()

        if selected == "选择预设模型...":
            return

        if selected == "自定义训练模型":
            self.browse_custom_model()
        else:
            # 处理预设模型
            model_mapping = {
                "YOLOv8n-seg (轻量级)": "yolov8n-seg.pt",
                "YOLOv8s-seg (小型)": "yolov8s-seg.pt",
                "YOLOv8m-seg (中型)": "yolov8m-seg.pt",
                "YOLOv8l-seg (大型)": "yolov8l-seg.pt",
                "YOLOv8x-seg (超大型)": "yolov8x-seg.pt"
            }

            if selected in model_mapping:
                model_file = model_mapping[selected]

                # 检查模型是否存在于常见位置
                possible_paths = [
                    model_file,  # 当前目录
                    f"models/{model_file}",  # models目录
                    f"weights/{model_file}",  # weights目录
                    os.path.expanduser(f"~/.ultralytics/{model_file}")  # 用户目录
                ]

                found_path = None
                for path in possible_paths:
                    if os.path.exists(path):
                        found_path = path
                        break

                if found_path:
                    self.custom_model_path_edit.setText(found_path)
                    self.custom_model_info_label.setText(f"预设模型: {selected}")
                else:
                    # 提示下载
                    reply = QMessageBox.question(
                        self, "模型不存在",
                        f"预设模型 {model_file} 不存在。\n是否要自动下载？",
                        QMessageBox.Yes | QMessageBox.No
                    )

                    if reply == QMessageBox.Yes:
                        self.download_preset_model(model_file)

    def download_preset_model(self, model_file):
        """下载预设模型"""
        try:
            from ultralytics import YOLO

            # 创建进度对话框
            progress = QProgressDialog(f"正在下载 {model_file}...", "取消", 0, 0, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            # 下载模型
            model = YOLO(model_file)  # 这会自动下载模型

            progress.close()

            # 设置路径
            model_path = model.ckpt_path if hasattr(model, 'ckpt_path') else model_file
            self.custom_model_path_edit.setText(model_path)
            self.custom_model_info_label.setText(f"预设模型: {model_file} | ✅ 已下载")

            QMessageBox.information(self, "下载成功", f"模型 {model_file} 下载完成！")

        except Exception as e:
            if 'progress' in locals():
                progress.close()
            QMessageBox.critical(self, "下载失败", f"无法下载模型: {str(e)}")

    def load_custom_model_config(self):
        """加载保存的自定义模型配置"""
        try:
            config_file = "config/custom_model_config.json"

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                custom_model_path = config.get('custom_model_path', '')

                if custom_model_path and os.path.exists(custom_model_path):
                    self.custom_model_path_edit.setText(custom_model_path)
                    model_name = config.get('model_name', os.path.basename(custom_model_path))
                    size_mb = config.get('model_size_mb', 0)

                    self.custom_model_info_label.setText(
                        f"自定义模型: {model_name} | {size_mb:.1f}MB | 💾 已保存配置"
                    )

                    print(f"✅ 已加载自定义模型配置: {model_name}")
                else:
                    print("⚠️  配置文件中的模型路径无效")
            else:
                print("ℹ️  未找到自定义模型配置文件")

        except Exception as e:
            print(f"❌ 加载自定义模型配置失败: {e}")

    def setup_splitter(self):
        """配置分割器属性"""
        # 设置分割器样式
        self.main_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #e0e0e0;
                border: 1px solid #c0c0c0;
                width: 6px;
                margin: 2px;
                border-radius: 3px;
            }
            QSplitter::handle:hover {
                background-color: #d0d0d0;
                border: 1px solid #a0a0a0;
            }
            QSplitter::handle:pressed {
                background-color: #c0c0c0;
                border: 1px solid #808080;
            }
        """)

        # 设置分割器属性
        self.main_splitter.setHandleWidth(6)  # 设置拖拽手柄宽度
        self.main_splitter.setChildrenCollapsible(True)  # 允许折叠子部件
        self.main_splitter.setOpaqueResize(True)  # 实时调整大小

        # 连接分割器信号
        self.main_splitter.splitterMoved.connect(self.on_splitter_moved)

    def configure_splitter_layout(self):
        """配置分割器布局和约束"""
        # 设置初始比例 (左侧:右侧 = 30:70)
        total_width = 1400  # 窗口总宽度
        left_width = int(total_width * 0.3)  # 左侧30%
        right_width = int(total_width * 0.7)  # 右侧70%

        self.main_splitter.setSizes([left_width, right_width])

        # 设置最小宽度约束
        if self.main_splitter.count() >= 2:
            # 左侧面板最小宽度300px，最大宽度600px
            left_widget = self.main_splitter.widget(0)
            if left_widget:
                left_widget.setMinimumWidth(300)
                left_widget.setMaximumWidth(800)

            # 右侧显示区域最小宽度500px
            right_widget = self.main_splitter.widget(1)
            if right_widget:
                right_widget.setMinimumWidth(500)

        # 保存初始状态
        self.save_splitter_state()

    def on_splitter_moved(self, pos, index):
        """分割器移动时的回调"""
        sizes = self.main_splitter.sizes()
        total_width = sum(sizes)

        if total_width > 0:
            left_ratio = sizes[0] / total_width
            right_ratio = sizes[1] / total_width

            # 更新状态栏显示
            self.statusBar().showMessage(
                f"面板比例 - 左侧: {left_ratio:.1%} | 右侧: {right_ratio:.1%}"
            )

            # 自动保存布局状态
            self.save_splitter_state()

    def save_splitter_state(self):
        """保存分割器状态"""
        try:
            import json

            # 创建配置目录
            config_dir = "config"
            os.makedirs(config_dir, exist_ok=True)

            # 保存分割器状态
            splitter_state = {
                'sizes': self.main_splitter.sizes(),
                'geometry': [self.x(), self.y(), self.width(), self.height()],
                'saved_time': datetime.now().isoformat()
            }

            config_file = os.path.join(config_dir, "splitter_layout.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(splitter_state, f, indent=2)

        except Exception as e:
            print(f"保存布局状态失败: {e}")

    def load_splitter_state(self):
        """加载分割器状态"""
        try:
            config_file = "config/splitter_layout.json"

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    splitter_state = json.load(f)

                # 恢复分割器大小
                if 'sizes' in splitter_state:
                    sizes = splitter_state['sizes']
                    if len(sizes) == 2 and all(s > 0 for s in sizes):
                        self.main_splitter.setSizes(sizes)

                # 恢复窗口几何
                if 'geometry' in splitter_state:
                    geo = splitter_state['geometry']
                    if len(geo) == 4:
                        self.setGeometry(geo[0], geo[1], geo[2], geo[3])

                print("✅ 已恢复界面布局")

        except Exception as e:
            print(f"加载布局状态失败: {e}")

    def create_layout_control_group(self, layout):
        """创建界面布局控制组"""
        group = QGroupBox("🖥️ 界面布局")
        group_layout = QVBoxLayout(group)

        # 布局预设按钮
        preset_layout = QHBoxLayout()

        self.layout_compact_btn = QPushButton("紧凑")
        self.layout_compact_btn.clicked.connect(lambda: self.apply_layout_preset("compact"))
        self.layout_compact_btn.setToolTip("紧凑布局 - 左侧面板较窄")
        preset_layout.addWidget(self.layout_compact_btn)

        self.layout_balanced_btn = QPushButton("平衡")
        self.layout_balanced_btn.clicked.connect(lambda: self.apply_layout_preset("balanced"))
        self.layout_balanced_btn.setToolTip("平衡布局 - 左右面板均衡")
        preset_layout.addWidget(self.layout_balanced_btn)

        self.layout_wide_btn = QPushButton("宽屏")
        self.layout_wide_btn.clicked.connect(lambda: self.apply_layout_preset("wide"))
        self.layout_wide_btn.setToolTip("宽屏布局 - 左侧面板较宽")
        preset_layout.addWidget(self.layout_wide_btn)

        group_layout.addLayout(preset_layout)

        # 重置和保存按钮
        control_layout = QHBoxLayout()

        self.reset_layout_btn = QPushButton("🔄 重置")
        self.reset_layout_btn.clicked.connect(self.reset_layout)
        self.reset_layout_btn.setToolTip("重置为默认布局")
        control_layout.addWidget(self.reset_layout_btn)

        self.save_layout_btn = QPushButton("💾 保存")
        self.save_layout_btn.clicked.connect(self.save_splitter_state)
        self.save_layout_btn.setToolTip("保存当前布局设置")
        control_layout.addWidget(self.save_layout_btn)

        group_layout.addLayout(control_layout)

        # 布局信息显示
        self.layout_info_label = QLabel("当前布局: 默认")
        self.layout_info_label.setStyleSheet("color: #666; font-size: 11px;")
        group_layout.addWidget(self.layout_info_label)

        # 折叠控制
        collapse_layout = QHBoxLayout()

        self.collapse_left_btn = QPushButton("◀ 折叠左侧")
        self.collapse_left_btn.clicked.connect(self.toggle_left_panel)
        self.collapse_left_btn.setToolTip("折叠/展开左侧控制面板")
        collapse_layout.addWidget(self.collapse_left_btn)

        self.fullscreen_btn = QPushButton("🔳 全屏")
        self.fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        self.fullscreen_btn.setToolTip("切换全屏模式")
        collapse_layout.addWidget(self.fullscreen_btn)

        group_layout.addLayout(collapse_layout)

        layout.addWidget(group)

    def apply_layout_preset(self, preset_name):
        """应用布局预设"""
        total_width = self.width()

        if preset_name == "compact":
            # 紧凑布局: 25% : 75%
            left_width = int(total_width * 0.25)
            right_width = int(total_width * 0.75)
            self.layout_info_label.setText("当前布局: 紧凑模式")
        elif preset_name == "balanced":
            # 平衡布局: 35% : 65%
            left_width = int(total_width * 0.35)
            right_width = int(total_width * 0.65)
            self.layout_info_label.setText("当前布局: 平衡模式")
        elif preset_name == "wide":
            # 宽屏布局: 45% : 55%
            left_width = int(total_width * 0.45)
            right_width = int(total_width * 0.55)
            self.layout_info_label.setText("当前布局: 宽屏模式")
        else:
            return

        self.main_splitter.setSizes([left_width, right_width])
        self.save_splitter_state()

        # 更新状态栏
        self.statusBar().showMessage(f"已应用{preset_name}布局预设")

    def reset_layout(self):
        """重置为默认布局"""
        # 删除保存的配置文件
        config_file = "config/splitter_layout.json"
        if os.path.exists(config_file):
            try:
                os.remove(config_file)
                print("已删除保存的布局配置")
            except Exception as e:
                print(f"删除布局配置失败: {e}")

        # 重置为默认布局
        self.configure_splitter_layout()
        self.layout_info_label.setText("当前布局: 默认")
        self.statusBar().showMessage("已重置为默认布局")

    def toggle_left_panel(self):
        """切换左侧面板的折叠状态"""
        sizes = self.main_splitter.sizes()

        if sizes[0] > 50:  # 左侧面板展开状态
            # 折叠左侧面板
            self.main_splitter.setSizes([0, sum(sizes)])
            self.collapse_left_btn.setText("▶ 展开左侧")
            self.layout_info_label.setText("当前布局: 左侧已折叠")
        else:  # 左侧面板折叠状态
            # 展开左侧面板
            total_width = sum(sizes)
            left_width = int(total_width * 0.3)
            right_width = total_width - left_width
            self.main_splitter.setSizes([left_width, right_width])
            self.collapse_left_btn.setText("◀ 折叠左侧")
            self.layout_info_label.setText("当前布局: 左侧已展开")

        self.save_splitter_state()

    def toggle_fullscreen(self):
        """切换全屏模式"""
        if self.isFullScreen():
            self.showNormal()
            self.fullscreen_btn.setText("🔳 全屏")
            self.statusBar().showMessage("已退出全屏模式")
        else:
            self.showFullScreen()
            self.fullscreen_btn.setText("🗗 窗口")
            self.statusBar().showMessage("已进入全屏模式")

    def setup_shortcuts(self):
        """设置键盘快捷键"""
        from PyQt5.QtWidgets import QShortcut
        from PyQt5.QtGui import QKeySequence

        # 布局快捷键
        QShortcut(QKeySequence("Ctrl+1"), self, lambda: self.apply_layout_preset("compact"))
        QShortcut(QKeySequence("Ctrl+2"), self, lambda: self.apply_layout_preset("balanced"))
        QShortcut(QKeySequence("Ctrl+3"), self, lambda: self.apply_layout_preset("wide"))

        # 面板控制快捷键
        QShortcut(QKeySequence("Ctrl+H"), self, self.toggle_left_panel)  # Hide/Show
        QShortcut(QKeySequence("F11"), self, self.toggle_fullscreen)     # Fullscreen
        QShortcut(QKeySequence("Ctrl+R"), self, self.reset_layout)       # Reset

        # 功能快捷键
        QShortcut(QKeySequence("Ctrl+O"), self, self.browse_image)       # Open
        QShortcut(QKeySequence("Ctrl+D"), self, self.start_detection)    # Detect
        QShortcut(QKeySequence("Ctrl+S"), self, self.save_splitter_state) # Save layout

        print("✅ 键盘快捷键已设置")
        print("快捷键说明:")
        print("  Ctrl+1/2/3: 切换布局预设")
        print("  Ctrl+H: 折叠/展开左侧面板")
        print("  F11: 全屏模式")
        print("  Ctrl+R: 重置布局")
        print("  Ctrl+O: 打开图像")
        print("  Ctrl+D: 开始检测")
        print("  Ctrl+S: 保存布局")

    def create_action_buttons_group(self, layout):
        """创建操作按钮组"""
        group = QGroupBox("操作")
        group_layout = QVBoxLayout(group)
        
        # 开始检测按钮
        self.detect_btn = QPushButton("开始检测")
        self.detect_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.detect_btn.clicked.connect(self.start_detection)
        group_layout.addWidget(self.detect_btn)
        
        # 批量处理按钮
        self.batch_btn = QPushButton("批量处理")
        self.batch_btn.clicked.connect(self.start_batch_processing)
        group_layout.addWidget(self.batch_btn)
        
        # 停止处理按钮
        self.stop_btn = QPushButton("停止处理")
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_processing)
        group_layout.addWidget(self.stop_btn)
        
        # 清除结果按钮
        clear_btn = QPushButton("清除结果")
        clear_btn.clicked.connect(self.clear_results)
        group_layout.addWidget(clear_btn)
        
        layout.addWidget(group)
    
    def create_results_display_group(self, layout):
        """创建结果显示组"""
        group = QGroupBox("检测结果")
        group_layout = QVBoxLayout(group)
        
        # 结果文本显示
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(200)
        self.results_text.setReadOnly(True)
        group_layout.addWidget(self.results_text)
        
        layout.addWidget(group)
    
    def create_display_area(self, parent):
        """创建右侧显示区域"""
        display_widget = QWidget()
        display_layout = QVBoxLayout(display_widget)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 原始图像标签页
        self.original_tab = self.create_image_tab("原始图像")
        self.tab_widget.addTab(self.original_tab, "原始图像")
        
        # 检测结果标签页
        self.result_tab = self.create_image_tab("检测结果")
        self.tab_widget.addTab(self.result_tab, "检测结果")
        
        # 宽度结果标签页
        self.width_tab = self.create_image_tab("宽度结果")
        self.tab_widget.addTab(self.width_tab, "宽度结果")
        
        display_layout.addWidget(self.tab_widget)
        parent.addWidget(display_widget)
    
    def create_image_tab(self, title):
        """创建图像显示标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 图像显示标签
        image_label = QLabel()
        image_label.setAlignment(Qt.AlignCenter)
        image_label.setMinimumSize(800, 600)
        image_label.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;")
        image_label.setText(f"等待{title}...")
        
        # 添加到滚动区域
        scroll = QScrollArea()
        scroll.setWidget(image_label)
        scroll.setWidgetResizable(True)
        
        layout.addWidget(scroll)
        
        # 保存引用
        if title == "原始图像":
            self.original_image_label = image_label
        elif title == "检测结果":
            self.result_image_label = image_label
        elif title == "宽度结果":
            self.width_image_label = image_label
        
        return tab

    def init_detector(self):
        """初始化检测器"""
        try:
            # 获取选中的模型
            model_id = None
            yolo_model_path = None

            if hasattr(self, 'model_combo'):
                selected_model = self.model_combo.currentData()
                if selected_model and selected_model != "auto":
                    if str(selected_model).startswith("custom_"):
                        # 自定义模型
                        yolo_model_path = str(selected_model).replace("custom_", "")
                        model_id = None  # 使用路径而不是ID
                    else:
                        model_id = selected_model

            # 检查是否有自定义模型路径
            if hasattr(self, 'custom_model_path_edit'):
                custom_path = self.custom_model_path_edit.text().strip()
                if custom_path and os.path.exists(custom_path):
                    yolo_model_path = custom_path
                    model_id = None

            self.detector = EnhancedCrackDetector(
                scale_factor=0.1,
                unit='mm²',
                yolo_model_path=yolo_model_path,
                yolo_model_id=model_id,
                enable_pavement_detection=True,
                enable_concrete_detection=True
            )

            # 初始化最优分析系统
            self.optimal_system = OptimalCrackAnalysisSystem(
                scale_factor=0.1,
                unit='mm'
            )

            # 显示当前模型信息
            model_info = self.detector.get_current_model_info()
            if model_info:
                status_msg = f"检测器初始化成功 - 模型: {model_info['name']}"
            else:
                status_msg = "检测器初始化成功 (仅传统方法)"

            self.statusBar().showMessage(status_msg)

        except Exception as e:
            QMessageBox.warning(self, "警告", f"检测器初始化失败: {str(e)}")
            self.statusBar().showMessage("检测器初始化失败")

    def browse_image(self):
        """浏览选择图像文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图像文件", "",
            "图像文件 (*.jpg *.jpeg *.png *.bmp *.tiff *.tif);;所有文件 (*)"
        )

        if file_path:
            self.image_path_edit.setText(file_path)
            self.current_image_path = file_path
            self.load_and_display_image(file_path)

    def browse_folder(self):
        """浏览选择文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择图片文件夹")

        if folder_path:
            self.folder_path_edit.setText(folder_path)

    def load_and_display_image(self, image_path):
        """加载并显示图像"""
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                QMessageBox.warning(self, "错误", "无法读取图像文件")
                return

            # 转换为RGB格式
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # 显示原始图像
            self.display_image(image_rgb, self.original_image_label)

            # 清除之前的结果
            self.result_image_label.setText("等待检测结果...")
            self.width_image_label.setText("等待宽度结果...")

            self.statusBar().showMessage(f"已加载图像: {os.path.basename(image_path)}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载图像失败: {str(e)}")

    def display_image(self, image, label):
        """在标签中显示图像"""
        try:
            height, width, channel = image.shape
            bytes_per_line = 3 * width

            q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format_RGB888)

            # 缩放图像以适应标签大小
            label_size = label.size()
            scaled_pixmap = QPixmap.fromImage(q_image).scaled(
                label_size, Qt.KeepAspectRatio, Qt.SmoothTransformation
            )

            label.setPixmap(scaled_pixmap)

        except Exception as e:
            print(f"显示图像失败: {e}")
            label.setText("图像显示失败")

    def apply_parameters(self):
        """应用参数设置"""
        if not self.detector:
            QMessageBox.warning(self, "警告", "检测器未初始化")
            return

        try:
            # 更新比例尺和单位
            scale_factor = self.scale_factor_spin.value()
            unit = self.unit_combo.currentText()

            self.detector.set_scale_factor(scale_factor)
            self.detector.set_unit(unit)

            # 更新轮廓筛选参数
            self.detector.update_contour_filter_params(
                min_area=self.min_area_spin.value(),
                max_area=self.max_area_spin.value(),
                min_circularity=self.min_circularity_spin.value(),
                max_circularity=self.max_circularity_spin.value()
            )

            self.statusBar().showMessage("参数已更新")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"参数更新失败: {str(e)}")

    def start_detection(self):
        """开始检测"""
        if not self.current_image_path:
            QMessageBox.warning(self, "警告", "请先选择图像文件")
            return

        if not self.detector:
            QMessageBox.warning(self, "警告", "检测器未初始化")
            return

        # 应用当前参数
        self.apply_parameters()

        # 禁用按钮
        self.detect_btn.setEnabled(False)
        self.batch_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 确定检测方法
        use_pavement = self.use_pavement_cb.isChecked()
        use_concrete = self.use_concrete_cb.isChecked()

        # 获取分析模式
        mode_text = self.analysis_mode_combo.currentText()
        analysis_mode = None
        if mode_text == "快速模式":
            analysis_mode = AnalysisMode.FAST
        elif mode_text == "平衡模式":
            analysis_mode = AnalysisMode.BALANCED
        elif mode_text == "精确模式":
            analysis_mode = AnalysisMode.PRECISE
        elif mode_text == "研究模式":
            analysis_mode = AnalysisMode.RESEARCH

        # 创建处理线程
        self.processing_thread = ImageProcessingThread(
            self.detector,
            self.current_image_path,
            use_yolo=self.use_yolo_cb.isChecked(),
            use_pavement_detection=use_pavement,
            use_concrete_detection=use_concrete,
            calculate_width=self.calculate_width_cb.isChecked(),
            optimal_system=self.optimal_system,
            analysis_mode=analysis_mode
        )

        # 连接信号
        self.processing_thread.finished.connect(self.on_detection_finished)
        self.processing_thread.progress.connect(self.progress_bar.setValue)
        self.processing_thread.status_update.connect(self.statusBar().showMessage)

        # 启动线程
        self.processing_thread.start()

    def start_batch_processing(self):
        """开始批量处理"""
        folder_path = self.folder_path_edit.text()
        if not folder_path:
            QMessageBox.warning(self, "警告", "请先选择图片文件夹")
            return

        if not self.detector:
            QMessageBox.warning(self, "警告", "检测器未初始化")
            return

        # 应用当前参数
        self.apply_parameters()

        # 禁用按钮
        self.detect_btn.setEnabled(False)
        self.batch_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 创建批量处理线程
        self.batch_thread = BatchProcessingThread(
            self.detector,
            folder_path,
            use_yolo=self.use_yolo_cb.isChecked()
        )

        # 连接信号
        self.batch_thread.finished.connect(self.on_batch_finished)
        self.batch_thread.progress.connect(self.progress_bar.setValue)
        self.batch_thread.status_update.connect(self.statusBar().showMessage)

        # 启动线程
        self.batch_thread.start()

    def stop_processing(self):
        """停止处理"""
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.terminate()
            self.processing_thread.wait()

        if self.batch_thread and self.batch_thread.isRunning():
            self.batch_thread.terminate()
            self.batch_thread.wait()

        self.reset_ui_state()
        self.statusBar().showMessage("处理已停止")

    def reset_ui_state(self):
        """重置UI状态"""
        self.detect_btn.setEnabled(True)
        self.batch_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)

    def on_detection_finished(self, result):
        """检测完成回调"""
        self.reset_ui_state()
        self.current_result = result

        if 'error' in result:
            error_msg = result['error']

            # 构建详细的错误信息
            detailed_msg = f"检测失败: {error_msg}\n\n"

            # 添加建议
            if 'suggestions' in result:
                detailed_msg += "建议尝试:\n"
                for suggestion in result['suggestions']:
                    detailed_msg += f"• {suggestion}\n"

            if 'recommended_actions' in result:
                detailed_msg += "\n推荐操作:\n"
                for action in result['recommended_actions']:
                    detailed_msg += f"• {action}\n"

            # 显示错误对话框
            msg_box = QMessageBox(self)
            msg_box.setIcon(QMessageBox.Warning)
            msg_box.setWindowTitle("检测失败")
            msg_box.setText("检测过程中遇到问题")
            msg_box.setDetailedText(detailed_msg)
            msg_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Help)

            if msg_box.exec_() == QMessageBox.Help:
                # 显示帮助信息
                help_msg = """
检测失败常见解决方案:

1. 降低置信度阈值
   - 在参数设置中将置信度从0.25降低到0.15-0.2

2. 切换检测方法
   - 取消勾选"使用YOLO分割"
   - 尝试使用传统检测方法

3. 图像质量检查
   - 确保图像对比度足够
   - 裂缝应该清晰可见
   - 避免过度曝光或过暗

4. 参数调整
   - 降低最小面积阈值
   - 调整圆形度范围

5. 分析模式
   - 尝试切换到"精确模式"或"研究模式"
                """
                QMessageBox.information(self, "帮助", help_msg)

            self.results_text.setText(detailed_msg)
            return

        # 显示检测结果
        self.display_detection_results(result)

        self.statusBar().showMessage("检测完成")

    def display_detection_results(self, result):
        """显示检测结果"""
        try:
            # 显示结果图像
            if 'result_image' in result:
                result_image = result['result_image']
                result_image_rgb = cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB)
                self.display_image(result_image_rgb, self.result_image_label)
            else:
                # 如果没有结果图像，显示原图
                if self.current_image_path:
                    original_image = cv2.imread(self.current_image_path)
                    if original_image is not None:
                        original_rgb = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
                        self.display_image(original_rgb, self.result_image_label)

                        # 在图像上添加"无可视化结果"的提示
                        from crack_detection import put_chinese_text
                        overlay_image = original_image.copy()
                        overlay_image = put_chinese_text(overlay_image, "检测完成 - 查看文本结果",
                                                        (10, 30), 24, (0, 255, 0))
                        overlay_rgb = cv2.cvtColor(overlay_image, cv2.COLOR_BGR2RGB)
                        self.display_image(overlay_rgb, self.result_image_label)

            # 显示宽度结果（如果有）
            if 'width_visualization' in result:
                width_image = result['width_visualization']
                width_image_rgb = cv2.cvtColor(width_image, cv2.COLOR_BGR2RGB)
                self.display_image(width_image_rgb, self.width_image_label)
            else:
                # 清空宽度图像显示
                self.width_image_label.clear()
                self.width_image_label.setText("无宽度分析结果")

            # 显示文本结果
            self.display_text_results(result)

        except Exception as e:
            error_msg = f"显示结果失败: {str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

            # 尝试至少显示文本结果
            try:
                self.display_text_results(result)
            except:
                self.results_text.setText(f"显示失败，但检测可能已完成。错误: {str(e)}")

    def display_text_results(self, result):
        """显示文本结果"""
        try:
            # 获取面积信息，支持不同的结果格式
            area_info = result.get('area_info') or result.get('area_analysis')
            if not area_info:
                self.results_text.setText("无法获取检测结果信息")
                return

            text_result = []

            text_result.append("=== 检测结果 ===")
            text_result.append(f"图像路径: {result.get('image_path', '未知')}")
            text_result.append(f"处理时间: {result.get('processing_time', 0):.2f}秒")
            text_result.append(f"检测方法: {result.get('detection_method', '未知')}")
            text_result.append("")

            text_result.append("=== 面积信息 ===")
            text_result.append(f"裂缝数量: {area_info.get('contour_count', 0)}")

            # 处理不同的面积字段名
            total_pixel_area = area_info.get('total_pixel_area', 0)
            total_real_area = area_info.get('total_real_area', 0)
            unit = result.get('unit') or area_info.get('unit', 'mm²')

            text_result.append(f"总像素面积: {total_pixel_area:.0f} 像素²")
            text_result.append(f"总实际面积: {total_real_area:.2f} {unit}")

            if area_info.get('contour_count', 0) > 0:
                stats = area_info.get('area_statistics', {})
                if stats:
                    # 处理不同的统计字段名
                    mean_area = stats.get('mean_real_area') or stats.get('mean_area', 0)
                    max_area = stats.get('max_real_area') or stats.get('max_area', 0)
                    min_area = stats.get('min_real_area') or stats.get('min_area', 0)
                    std_area = stats.get('std_real_area') or stats.get('std_area', 0)

                    text_result.append(f"平均面积: {mean_area:.2f} {unit}")
                    text_result.append(f"最大面积: {max_area:.2f} {unit}")
                    text_result.append(f"最小面积: {min_area:.2f} {unit}")
                    text_result.append(f"面积标准差: {std_area:.2f} {unit}")

                    # 如果有中位数信息
                    if 'median_area' in stats:
                        text_result.append(f"中位数面积: {stats['median_area']:.2f} {unit}")

            # 宽度信息（如果有）
            width_info = result.get('width_info') or result.get('width_analysis')
            optimal_width = result.get('optimal_width_analysis')
            max_width_info = result.get('max_width_analysis')

            if width_info or optimal_width or max_width_info:
                text_result.append("")
                text_result.append("=== 宽度分析结果 ===")

                # 传统宽度计算结果
                if width_info:
                    text_result.append("📏 传统宽度计算:")
                    width_count = width_info.get('width_count') or width_info.get('width_measurements', {}).get('measurement_points', 0)
                    text_result.append(f"  测量点数: {width_count}")

                    if width_count > 0:
                        stats = width_info.get('statistics') or width_info.get('width_statistics', {})
                        if stats:
                            width_unit = unit.replace('²', '')
                            text_result.append(f"  平均宽度: {stats.get('mean_width', 0):.3f} {width_unit}")
                            text_result.append(f"  最大宽度: {stats.get('max_width', 0):.3f} {width_unit}")
                            text_result.append(f"  最小宽度: {stats.get('min_width', 0):.3f} {width_unit}")
                            text_result.append(f"  宽度标准差: {stats.get('std_width', 0):.3f} {width_unit}")

                            if 'median_width' in stats:
                                text_result.append(f"  中位数宽度: {stats['median_width']:.3f} {width_unit}")

                            if width_info.get('total_length_real'):
                                text_result.append(f"  骨架线长度: {width_info['total_length_real']:.2f} {width_unit}")

                # 最优系统宽度分析结果
                if optimal_width:
                    text_result.append("")
                    text_result.append("🔬 最优系统宽度分析:")

                    width_measurements = optimal_width.get('width_measurements', {})
                    width_stats = optimal_width.get('width_statistics', {})

                    if width_measurements:
                        measurement_points = width_measurements.get('measurement_points', 0)
                        text_result.append(f"  高精度测量点: {measurement_points}")

                    if width_stats:
                        width_unit = optimal_width.get('unit', unit.replace('²', ''))
                        text_result.append(f"  平均宽度: {width_stats.get('mean_width', 0):.3f} {width_unit}")
                        text_result.append(f"  最大宽度: {width_stats.get('max_width', 0):.3f} {width_unit}")
                        text_result.append(f"  最小宽度: {width_stats.get('min_width', 0):.3f} {width_unit}")
                        text_result.append(f"  宽度标准差: {width_stats.get('std_width', 0):.3f} {width_unit}")
                        text_result.append(f"  中位数宽度: {width_stats.get('median_width', 0):.3f} {width_unit}")

                # 最大宽度分析结果（内切圆法）
                if max_width_info and not max_width_info.get('error'):
                    text_result.append("")
                    text_result.append("🔵 最大宽度分析 (内切圆法):")

                    max_width_unit = max_width_info.get('unit', unit.replace('²', ''))
                    text_result.append(f"  最大宽度: {max_width_info.get('max_width_real', 0):.3f} {max_width_unit}")
                    text_result.append(f"  最大宽度(像素): {max_width_info.get('max_width_pixel', 0):.1f} 像素")

                    # 显示统计信息
                    max_stats = max_width_info.get('statistics', {})
                    if max_stats:
                        text_result.append(f"  分析轮廓数: {max_stats.get('count', 0)}")
                        text_result.append(f"  平均宽度: {max_stats.get('mean_width_real', 0):.3f} {max_width_unit}")
                        text_result.append(f"  最小宽度: {max_stats.get('min_width_real', 0):.3f} {max_width_unit}")
                        text_result.append(f"  宽度标准差: {max_stats.get('std_width_real', 0):.3f} {max_width_unit}")
                        text_result.append(f"  中位数宽度: {max_stats.get('median_width_real', 0):.3f} {max_width_unit}")

                    # 显示处理时间
                    processing_time = max_width_info.get('processing_time', 0)
                    text_result.append(f"  计算时间: {processing_time:.2f} 秒")

                    # 显示最大内切圆信息
                    max_circle = max_width_info.get('max_circle', {})
                    if max_circle and max_circle.get('success'):
                        center = max_circle.get('center', (0, 0))
                        text_result.append(f"  最大内切圆中心: ({center[0]:.1f}, {center[1]:.1f})")
                        text_result.append(f"  最大内切圆半径: {max_circle.get('radius', 0):.1f} 像素")

                # 宽度分析建议
                if width_info or optimal_width:
                    text_result.append("")
                    text_result.append("💡 宽度分析建议:")

                    # 根据宽度数据给出建议
                    if width_info and width_info.get('statistics'):
                        mean_width = width_info['statistics'].get('mean_width', 0)
                        std_width = width_info['statistics'].get('std_width', 0)

                        if mean_width > 0:
                            cv = std_width / mean_width if mean_width > 0 else 0
                            if cv > 0.5:
                                text_result.append("  • 宽度变化较大，建议检查裂缝发展情况")
                            elif cv < 0.2:
                                text_result.append("  • 宽度相对均匀，裂缝发展稳定")
                            else:
                                text_result.append("  • 宽度变化适中，属于正常范围")

                            if mean_width < 0.1:
                                text_result.append("  • 裂缝较细，可能处于早期发展阶段")
                            elif mean_width > 1.0:
                                text_result.append("  • 裂缝较宽，建议重点关注")
                            else:
                                text_result.append("  • 裂缝宽度在常见范围内")

            # 质量评估信息（如果有）
            if 'quality_assessment' in result and result['quality_assessment']:
                quality = result['quality_assessment']
                text_result.append("")
                text_result.append("=== 质量评估 ===")
                text_result.append(f"质量评分: {quality.get('quality_score', 0):.3f}")
                text_result.append(f"质量等级: {quality.get('quality_level', '未知')}")

                if quality.get('issues'):
                    text_result.append("发现的问题:")
                    for issue in quality['issues']:
                        text_result.append(f"  • {issue}")

            # 形态学分析信息（如果有）
            if 'morphology_analysis' in result and result['morphology_analysis']:
                morph = result['morphology_analysis']
                if morph.get('total_contours', 0) > 0:
                    text_result.append("")
                    text_result.append("=== 形态学分析 ===")
                    text_result.append(f"分析轮廓数: {morph['total_contours']}")

            text_result.append("")
            scale_factor = result.get('scale_factor') or area_info.get('scale_factor', 0.1)
            text_result.append(f"比例尺: 1像素 = {scale_factor:.3f}{unit.replace('²', '')}")

            if result.get('save_path'):
                text_result.append(f"结果已保存至: {result['save_path']}")

            self.results_text.setText("\n".join(text_result))

        except Exception as e:
            self.results_text.setText(f"结果显示失败: {str(e)}")

    def on_batch_finished(self, results):
        """批量处理完成回调"""
        self.reset_ui_state()

        if not results:
            QMessageBox.warning(self, "警告", "批量处理失败或无结果")
            return

        # 统计结果
        successful = sum(1 for r in results if 'error' not in r)
        failed = len(results) - successful

        # 显示统计信息
        stats_text = []
        stats_text.append("=== 批量处理结果 ===")
        stats_text.append(f"总计: {len(results)} 张图像")
        stats_text.append(f"成功: {successful} 张")
        stats_text.append(f"失败: {failed} 张")
        stats_text.append("")

        if successful > 0:
            # 计算总体统计
            total_cracks = 0
            total_area = 0

            for result in results:
                if 'error' not in result:
                    area_info = result['area_info']
                    total_cracks += area_info['contour_count']
                    total_area += area_info['total_real_area']

            stats_text.append(f"总检测裂缝数: {total_cracks}")
            stats_text.append(f"总裂缝面积: {total_area:.2f} {self.detector.unit}")
            stats_text.append(f"平均每张图像裂缝数: {total_cracks/successful:.1f}")
            stats_text.append(f"平均每张图像面积: {total_area/successful:.2f} {self.detector.unit}")

        stats_text.append("")
        stats_text.append("详细结果已保存至CSV文件")

        self.results_text.setText("\n".join(stats_text))

        QMessageBox.information(self, "完成", f"批量处理完成！\n成功: {successful} 张\n失败: {failed} 张")
        self.statusBar().showMessage("批量处理完成")

    def clear_results(self):
        """清除结果"""
        self.result_image_label.setText("等待检测结果...")
        self.width_image_label.setText("等待宽度结果...")
        self.results_text.clear()
        self.current_result = None
        self.statusBar().showMessage("结果已清除")

    def closeEvent(self, event):
        """关闭事件"""
        # 停止所有线程
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.terminate()
            self.processing_thread.wait()

        if self.batch_thread and self.batch_thread.isRunning():
            self.batch_thread.terminate()
            self.batch_thread.wait()

        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("增强版裂缝检测系统")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("CrackDetection")

    # 设置全局字体为微软雅黑
    setup_font()

    # 创建主窗口
    window = EnhancedCrackDetectionGUI()
    window.show()

    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
