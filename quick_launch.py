# -*- coding: utf-8 -*-
"""
快速启动脚本 - 确保系统正常运行
"""

import os
import sys
import subprocess
from pathlib import Path

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    issues = []
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        issues.append("Python版本过低，需要3.7或更高版本")
    else:
        print(f"✅ Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = [
        ('cv2', 'opencv-python'),
        ('numpy', 'numpy'),
        ('torch', 'torch'),
        ('ultralytics', 'ultralytics'),
        ('PyQt5', 'PyQt5'),
        ('PIL', 'Pillow')
    ]
    
    for package, install_name in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            issues.append(f"缺少包: {install_name}")
            print(f"❌ {package} 未安装")
    
    return issues

def check_gpu():
    """检查GPU状态"""
    print("\n🔍 检查GPU状态...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"✅ GPU: {gpu_name} ({memory:.1f} GB)")
            return True
        else:
            print("⚠️  CUDA不可用，将使用CPU模式")
            return False
    except:
        print("❌ 无法检查GPU状态")
        return False

def check_test_images():
    """检查测试图像"""
    print("\n🔍 检查测试图像...")
    
    test_dir = Path('test_images')
    if not test_dir.exists():
        test_dir.mkdir()
        print("📁 创建test_images目录")
    
    test_images = list(test_dir.glob('*.jpg')) + list(test_dir.glob('*.png'))
    
    if test_images:
        print(f"✅ 找到 {len(test_images)} 个测试图像")
        for img in test_images[:3]:  # 显示前3个
            print(f"  📷 {img.name}")
        if len(test_images) > 3:
            print(f"  ... 还有 {len(test_images) - 3} 个")
    else:
        print("⚠️  没有找到测试图像")
        print("💡 您可以将图像文件放在 test_images/ 目录中")
    
    return len(test_images) > 0

def create_sample_image():
    """创建示例图像"""
    print("\n🎨 创建示例图像...")
    
    try:
        import cv2
        import numpy as np
        
        # 创建示例裂缝图像
        img = np.ones((400, 600, 3), dtype=np.uint8) * 200
        
        # 添加裂缝
        cv2.line(img, (50, 50), (550, 350), (0, 0, 0), 4)
        cv2.line(img, (200, 200), (400, 200), (0, 0, 0), 3)
        cv2.line(img, (300, 100), (300, 300), (0, 0, 0), 3)
        
        # 保存示例图像
        sample_path = 'test_images/sample_crack.jpg'
        os.makedirs('test_images', exist_ok=True)
        cv2.imwrite(sample_path, img)
        
        print(f"✅ 示例图像已创建: {sample_path}")
        return True
    except Exception as e:
        print(f"❌ 创建示例图像失败: {e}")
        return False

def launch_gui():
    """启动GUI"""
    print("\n🚀 启动GUI...")
    
    try:
        # 检查GUI文件是否存在
        if not os.path.exists('enhanced_crack_gui.py'):
            print("❌ GUI文件不存在: enhanced_crack_gui.py")
            return False
        
        # 启动GUI
        print("正在启动增强版裂缝检测系统...")
        print("请稍等，GUI界面即将打开...")
        
        # 使用subprocess启动GUI，这样不会阻塞当前脚本
        process = subprocess.Popen([sys.executable, 'enhanced_crack_gui.py'])
        
        print("✅ GUI已启动")
        print("\n📋 使用提示:")
        print("1. 点击'加载图片'选择要检测的图像")
        print("2. 在参数设置中选择合适的分析模式")
        print("3. 调整比例尺和其他参数")
        print("4. 点击'开始检测'执行分析")
        print("5. 在结果标签页查看检测结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")
        return False

def show_usage_tips():
    """显示使用提示"""
    print("\n💡 使用建议:")
    print("=" * 50)
    print("🎯 分析模式选择:")
    print("  • 快速模式: 基础检测，速度最快")
    print("  • 平衡模式: 推荐日常使用")
    print("  • 精确模式: 高精度分析")
    print("  • 研究模式: 最详细的分析")
    print()
    print("📏 参数设置:")
    print("  • 比例尺: 根据实际测量设置")
    print("  • 最小面积: 过滤小噪声")
    print("  • 面积单位: 选择合适的单位")
    print()
    print("🔧 故障排除:")
    print("  • 如果检测失败，系统会自动回退到传统方法")
    print("  • 可以尝试不同的分析模式")
    print("  • 确保图像中裂缝清晰可见")
    print("  • 查看错误对话框中的详细建议")

def main():
    """主函数"""
    print("🎉 裂缝检测系统快速启动")
    print("=" * 50)
    
    # 检查环境
    issues = check_environment()
    
    if issues:
        print("\n❌ 发现以下问题:")
        for issue in issues:
            print(f"  • {issue}")
        print("\n请先解决这些问题后再运行系统")
        return
    
    # 检查GPU
    has_gpu = check_gpu()
    
    # 检查测试图像
    has_test_images = check_test_images()
    
    # 如果没有测试图像，创建示例图像
    if not has_test_images:
        create_sample_image()
    
    # 显示系统状态
    print("\n📊 系统状态:")
    print(f"  GPU加速: {'✅ 可用' if has_gpu else '❌ 不可用 (使用CPU)'}")
    print(f"  测试图像: {'✅ 可用' if has_test_images else '✅ 已创建示例'}")
    print("  检测方法: ✅ YOLO分割 + 传统方法回退")
    print("  错误处理: ✅ 智能回退机制")
    print("  中文支持: ✅ 微软雅黑字体")
    
    # 显示使用提示
    show_usage_tips()
    
    # 询问是否启动GUI
    print("\n" + "=" * 50)
    choice = input("是否现在启动GUI? (Y/n): ").strip().lower()
    
    if choice in ['', 'y', 'yes']:
        success = launch_gui()
        if success:
            print("\n🎉 系统启动成功！")
            print("GUI窗口应该已经打开，如果没有请检查任务栏")
        else:
            print("\n❌ 启动失败，请检查错误信息")
    else:
        print("\n💡 您可以稍后运行以下命令启动GUI:")
        print("python enhanced_crack_gui.py")
    
    print("\n📚 更多帮助:")
    print("  • 查看 DETECTION_TROUBLESHOOTING.md 了解故障排除")
    print("  • 查看 FINAL_OPTIMIZATION_SUMMARY.md 了解系统功能")
    print("  • 查看 USAGE_GUIDE.md 了解详细使用方法")

if __name__ == '__main__':
    main()
