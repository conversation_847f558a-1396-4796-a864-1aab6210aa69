# -*- coding: utf-8 -*-
"""
深度学习算法模块
基于数据驱动的特征学习，包括图像分类、目标检测、语义分割算法

功能：
1. 图像分类算法 - 基于ResNet的裂缝区域分类
2. 目标检测算法 - 基于YOLO的裂缝定位和检测
3. 语义分割算法 - 基于U-Net的像素级裂缝分割
4. 模型训练和推理 - 完整的训练和推理流程
5. 结果后处理 - 非极大值抑制、置信度筛选等
"""

import cv2
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from torchvision.models import resnet50
from typing import Tuple, List, Dict, Optional, Union
import time
import os
from datetime import datetime
import json

try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    print("警告: ultralytics未安装，YOLO功能不可用")


class CrackClassificationModel(nn.Module):
    """基于ResNet的裂缝分类模型"""
    
    def __init__(self, num_classes: int = 2, pretrained: bool = True):
        """
        初始化分类模型
        
        Args:
            num_classes: 分类数量（有裂缝/无裂缝）
            pretrained: 是否使用预训练权重
        """
        super(CrackClassificationModel, self).__init__()
        
        # 使用ResNet50作为骨干网络
        self.backbone = resnet50(pretrained=pretrained)
        
        # 修改最后的全连接层
        in_features = self.backbone.fc.in_features
        self.backbone.fc = nn.Linear(in_features, num_classes)
        
        # 添加dropout防止过拟合
        self.dropout = nn.Dropout(0.5)
        
    def forward(self, x):
        """前向传播"""
        features = self.backbone.avgpool(self.backbone.layer4(
            self.backbone.layer3(self.backbone.layer2(
                self.backbone.layer1(self.backbone.maxpool(
                    self.backbone.relu(self.backbone.bn1(
                        self.backbone.conv1(x)
                    ))
                ))
            ))
        ))
        
        features = torch.flatten(features, 1)
        features = self.dropout(features)
        output = self.backbone.fc(features)
        
        return output


class UNetSegmentationModel(nn.Module):
    """基于U-Net的语义分割模型"""
    
    def __init__(self, in_channels: int = 3, out_channels: int = 1):
        """
        初始化U-Net模型
        
        Args:
            in_channels: 输入通道数
            out_channels: 输出通道数
        """
        super(UNetSegmentationModel, self).__init__()
        
        # 编码器（下采样）
        self.enc1 = self._conv_block(in_channels, 64)
        self.enc2 = self._conv_block(64, 128)
        self.enc3 = self._conv_block(128, 256)
        self.enc4 = self._conv_block(256, 512)
        
        # 瓶颈层
        self.bottleneck = self._conv_block(512, 1024)
        
        # 解码器（上采样）
        self.upconv4 = nn.ConvTranspose2d(1024, 512, 2, stride=2)
        self.dec4 = self._conv_block(1024, 512)
        
        self.upconv3 = nn.ConvTranspose2d(512, 256, 2, stride=2)
        self.dec3 = self._conv_block(512, 256)
        
        self.upconv2 = nn.ConvTranspose2d(256, 128, 2, stride=2)
        self.dec2 = self._conv_block(256, 128)
        
        self.upconv1 = nn.ConvTranspose2d(128, 64, 2, stride=2)
        self.dec1 = self._conv_block(128, 64)
        
        # 输出层
        self.final_conv = nn.Conv2d(64, out_channels, 1)
        
        # 池化层
        self.pool = nn.MaxPool2d(2)
        
    def _conv_block(self, in_channels: int, out_channels: int) -> nn.Sequential:
        """卷积块"""
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, 3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        """前向传播"""
        # 编码器
        enc1 = self.enc1(x)
        enc2 = self.enc2(self.pool(enc1))
        enc3 = self.enc3(self.pool(enc2))
        enc4 = self.enc4(self.pool(enc3))
        
        # 瓶颈层
        bottleneck = self.bottleneck(self.pool(enc4))
        
        # 解码器
        dec4 = self.upconv4(bottleneck)
        dec4 = torch.cat((dec4, enc4), dim=1)
        dec4 = self.dec4(dec4)
        
        dec3 = self.upconv3(dec4)
        dec3 = torch.cat((dec3, enc3), dim=1)
        dec3 = self.dec3(dec3)
        
        dec2 = self.upconv2(dec3)
        dec2 = torch.cat((dec2, enc2), dim=1)
        dec2 = self.dec2(dec2)
        
        dec1 = self.upconv1(dec2)
        dec1 = torch.cat((dec1, enc1), dim=1)
        dec1 = self.dec1(dec1)
        
        # 输出
        output = self.final_conv(dec1)
        return torch.sigmoid(output)


class DeepLearningCrackDetector:
    """深度学习裂缝检测器"""
    
    def __init__(self, device: str = 'auto'):
        """
        初始化深度学习检测器
        
        Args:
            device: 计算设备 ('auto', 'cpu', 'cuda')
        """
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
            
        print(f"使用设备: {self.device}")
        
        # 模型存储
        self.classification_model = None
        self.segmentation_model = None
        self.yolo_model = None
        
        # 图像预处理
        self.classification_transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        self.segmentation_transform = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((256, 256)),
            transforms.ToTensor()
        ])
    
    def load_classification_model(self, model_path: Optional[str] = None) -> bool:
        """
        加载分类模型
        
        Args:
            model_path: 模型权重路径
            
        Returns:
            success: 是否加载成功
        """
        try:
            self.classification_model = CrackClassificationModel(num_classes=2)
            
            if model_path and os.path.exists(model_path):
                checkpoint = torch.load(model_path, map_location=self.device)
                self.classification_model.load_state_dict(checkpoint)
                print(f"已加载分类模型: {model_path}")
            else:
                print("使用未训练的分类模型（仅用于演示）")
                
            self.classification_model.to(self.device)
            self.classification_model.eval()
            return True
            
        except Exception as e:
            print(f"加载分类模型失败: {e}")
            return False
    
    def load_segmentation_model(self, model_path: Optional[str] = None) -> bool:
        """
        加载分割模型
        
        Args:
            model_path: 模型权重路径
            
        Returns:
            success: 是否加载成功
        """
        try:
            self.segmentation_model = UNetSegmentationModel(in_channels=3, out_channels=1)
            
            if model_path and os.path.exists(model_path):
                checkpoint = torch.load(model_path, map_location=self.device)
                self.segmentation_model.load_state_dict(checkpoint)
                print(f"已加载分割模型: {model_path}")
            else:
                print("使用未训练的分割模型（仅用于演示）")
                
            self.segmentation_model.to(self.device)
            self.segmentation_model.eval()
            return True
            
        except Exception as e:
            print(f"加载分割模型失败: {e}")
            return False
    
    def load_yolo_model(self, model_path: str = "yolo11n-seg.pt") -> bool:
        """
        加载YOLO模型
        
        Args:
            model_path: YOLO模型路径
            
        Returns:
            success: 是否加载成功
        """
        if not YOLO_AVAILABLE:
            print("YOLO不可用，请安装ultralytics")
            return False
            
        try:
            self.yolo_model = YOLO(model_path)
            print(f"已加载YOLO模型: {model_path}")
            return True
            
        except Exception as e:
            print(f"加载YOLO模型失败: {e}")
            return False
    
    def classify_image_patches(self, image: np.ndarray, 
                             patch_size: int = 224,
                             stride: int = 112,
                             confidence_threshold: float = 0.5) -> Dict:
        """
        图像分类算法：将图像分割为小区域进行分类
        
        Args:
            image: 输入图像
            patch_size: 图像块大小
            stride: 滑动步长
            confidence_threshold: 置信度阈值
            
        Returns:
            result: 分类结果
        """
        if self.classification_model is None:
            if not self.load_classification_model():
                return {'error': '分类模型加载失败'}
        
        start_time = time.time()
        
        # 确保图像是BGR格式
        if len(image.shape) == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
        
        h, w = image_rgb.shape[:2]
        crack_patches = []
        confidence_map = np.zeros((h, w), dtype=np.float32)
        
        # 滑动窗口提取图像块
        for y in range(0, h - patch_size + 1, stride):
            for x in range(0, w - patch_size + 1, stride):
                patch = image_rgb[y:y+patch_size, x:x+patch_size]
                
                # 预处理
                patch_tensor = self.classification_transform(patch).unsqueeze(0).to(self.device)
                
                # 推理
                with torch.no_grad():
                    output = self.classification_model(patch_tensor)
                    probabilities = F.softmax(output, dim=1)
                    crack_prob = probabilities[0, 1].item()  # 裂缝类别概率
                
                # 更新置信度图
                confidence_map[y:y+patch_size, x:x+patch_size] = np.maximum(
                    confidence_map[y:y+patch_size, x:x+patch_size], crack_prob
                )
                
                # 记录高置信度的裂缝块
                if crack_prob > confidence_threshold:
                    crack_patches.append({
                        'bbox': (x, y, patch_size, patch_size),
                        'confidence': crack_prob
                    })
        
        # 生成二值掩码
        binary_mask = (confidence_map > confidence_threshold).astype(np.uint8) * 255
        
        processing_time = time.time() - start_time
        
        result = {
            'method': 'classification',
            'binary_mask': binary_mask,
            'confidence_map': confidence_map,
            'crack_patches': crack_patches,
            'statistics': {
                'patch_count': len(crack_patches),
                'processing_time': processing_time,
                'confidence_threshold': confidence_threshold
            }
        }
        
        return result

    def detect_with_yolo(self, image: np.ndarray,
                        confidence_threshold: float = 0.5,
                        iou_threshold: float = 0.45) -> Dict:
        """
        YOLO目标检测算法：定位裂缝并框选

        Args:
            image: 输入图像
            confidence_threshold: 置信度阈值
            iou_threshold: IoU阈值（用于NMS）

        Returns:
            result: 检测结果
        """
        if self.yolo_model is None:
            if not self.load_yolo_model():
                return {'error': 'YOLO模型加载失败'}

        start_time = time.time()

        try:
            # YOLO推理
            results = self.yolo_model(image, conf=confidence_threshold, iou=iou_threshold)

            detections = []
            masks = []

            for result in results:
                # 处理边界框
                if result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()
                    confidences = result.boxes.conf.cpu().numpy()

                    for i, (box, conf) in enumerate(zip(boxes, confidences)):
                        x1, y1, x2, y2 = box.astype(int)
                        detections.append({
                            'bbox': (x1, y1, x2 - x1, y2 - y1),
                            'confidence': float(conf),
                            'center': ((x1 + x2) // 2, (y1 + y2) // 2)
                        })

                # 处理分割掩码
                if result.masks is not None:
                    for mask in result.masks.data:
                        mask_np = mask.cpu().numpy()
                        # 调整掩码大小到原图尺寸
                        mask_resized = cv2.resize(mask_np, (image.shape[1], image.shape[0]))
                        masks.append(mask_resized)

            # 合并所有掩码
            if masks:
                combined_mask = np.zeros((image.shape[0], image.shape[1]), dtype=np.float32)
                for mask in masks:
                    combined_mask = np.maximum(combined_mask, mask)
                binary_mask = (combined_mask > 0.5).astype(np.uint8) * 255
            else:
                binary_mask = np.zeros((image.shape[0], image.shape[1]), dtype=np.uint8)

            processing_time = time.time() - start_time

            result = {
                'method': 'yolo_detection',
                'binary_mask': binary_mask,
                'detections': detections,
                'masks': masks,
                'statistics': {
                    'detection_count': len(detections),
                    'processing_time': processing_time,
                    'confidence_threshold': confidence_threshold,
                    'iou_threshold': iou_threshold
                }
            }

            return result

        except Exception as e:
            return {'error': f'YOLO检测失败: {str(e)}'}

    def segment_with_unet(self, image: np.ndarray,
                         threshold: float = 0.5) -> Dict:
        """
        U-Net语义分割算法：像素级裂缝识别

        Args:
            image: 输入图像
            threshold: 分割阈值

        Returns:
            result: 分割结果
        """
        if self.segmentation_model is None:
            if not self.load_segmentation_model():
                return {'error': '分割模型加载失败'}

        start_time = time.time()

        # 预处理
        if len(image.shape) == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)

        original_size = image_rgb.shape[:2]

        # 转换为tensor
        input_tensor = self.segmentation_transform(image_rgb).unsqueeze(0).to(self.device)

        # 推理
        with torch.no_grad():
            output = self.segmentation_model(input_tensor)
            probability_map = output.squeeze().cpu().numpy()

        # 调整回原始尺寸
        probability_map = cv2.resize(probability_map, (original_size[1], original_size[0]))

        # 生成二值掩码
        binary_mask = (probability_map > threshold).astype(np.uint8) * 255

        processing_time = time.time() - start_time

        result = {
            'method': 'unet_segmentation',
            'binary_mask': binary_mask,
            'probability_map': probability_map,
            'statistics': {
                'processing_time': processing_time,
                'threshold': threshold,
                'crack_pixel_count': np.sum(binary_mask > 0)
            }
        }

        return result

    def comprehensive_detection(self, image: np.ndarray,
                              method: str = 'yolo',
                              **kwargs) -> Dict:
        """
        综合检测方法

        Args:
            image: 输入图像
            method: 检测方法 ('classification', 'yolo', 'unet', 'ensemble')
            **kwargs: 其他参数

        Returns:
            result: 检测结果
        """
        if method == 'classification':
            return self.classify_image_patches(image, **kwargs)
        elif method == 'yolo':
            return self.detect_with_yolo(image, **kwargs)
        elif method == 'unet':
            return self.segment_with_unet(image, **kwargs)
        elif method == 'ensemble':
            return self._ensemble_detection(image, **kwargs)
        else:
            return {'error': f'不支持的检测方法: {method}'}

    def _ensemble_detection(self, image: np.ndarray, **kwargs) -> Dict:
        """
        集成检测方法：结合多种算法的结果

        Args:
            image: 输入图像
            **kwargs: 其他参数

        Returns:
            result: 集成检测结果
        """
        start_time = time.time()

        results = {}
        masks = []

        # 运行所有可用的检测方法
        methods = ['classification', 'yolo', 'unet']

        for method in methods:
            try:
                result = self.comprehensive_detection(image, method=method, **kwargs)
                if 'error' not in result:
                    results[method] = result
                    if 'binary_mask' in result:
                        masks.append(result['binary_mask'])
            except Exception as e:
                print(f"{method} 检测失败: {e}")

        if not masks:
            return {'error': '所有检测方法都失败了'}

        # 投票机制：多数算法认为是裂缝的像素才标记为裂缝
        ensemble_mask = np.zeros_like(masks[0], dtype=np.float32)
        for mask in masks:
            ensemble_mask += (mask > 0).astype(np.float32)

        # 至少有一半的算法认为是裂缝
        threshold = len(masks) / 2
        final_mask = (ensemble_mask >= threshold).astype(np.uint8) * 255

        processing_time = time.time() - start_time

        result = {
            'method': 'ensemble',
            'binary_mask': final_mask,
            'individual_results': results,
            'statistics': {
                'methods_used': list(results.keys()),
                'processing_time': processing_time,
                'ensemble_threshold': threshold
            }
        }

        return result

    def visualize_results(self, original_image: np.ndarray,
                         result: Dict,
                         save_path: Optional[str] = None) -> np.ndarray:
        """
        可视化检测结果

        Args:
            original_image: 原始图像
            result: 检测结果
            save_path: 保存路径（可选）

        Returns:
            visualization: 可视化图像
        """
        if 'error' in result:
            print(f"检测错误: {result['error']}")
            return original_image

        # 创建可视化图像
        if len(original_image.shape) == 3:
            visualization = original_image.copy()
        else:
            visualization = cv2.cvtColor(original_image, cv2.COLOR_GRAY2BGR)

        # 叠加掩码
        if 'binary_mask' in result:
            mask = result['binary_mask']
            # 创建彩色掩码
            colored_mask = np.zeros_like(visualization)
            colored_mask[:, :, 2] = mask  # 红色通道

            # 半透明叠加
            alpha = 0.3
            visualization = cv2.addWeighted(visualization, 1-alpha, colored_mask, alpha, 0)

        # 绘制检测框（如果有）
        if 'detections' in result:
            for detection in result['detections']:
                bbox = detection['bbox']
                confidence = detection['confidence']

                x, y, w, h = bbox
                cv2.rectangle(visualization, (x, y), (x+w, y+h), (0, 255, 0), 2)

                # 添加置信度标签
                label = f"{confidence:.2f}"
                cv2.putText(visualization, label, (x, y-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # 添加统计信息
        if 'statistics' in result:
            stats = result['statistics']
            method = result.get('method', 'unknown')

            info_text = [f"Method: {method}"]

            if 'processing_time' in stats:
                info_text.append(f"Time: {stats['processing_time']:.3f}s")

            if 'detection_count' in stats:
                info_text.append(f"Detections: {stats['detection_count']}")
            elif 'patch_count' in stats:
                info_text.append(f"Patches: {stats['patch_count']}")
            elif 'crack_pixel_count' in stats:
                info_text.append(f"Pixels: {stats['crack_pixel_count']}")

            # 绘制文本
            y_offset = 30
            for i, text in enumerate(info_text):
                cv2.putText(visualization, text, (10, y_offset + i * 25),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                cv2.putText(visualization, text, (10, y_offset + i * 25),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)

        # 保存结果
        if save_path:
            cv2.imwrite(save_path, visualization)

        return visualization


def demo_deep_learning_algorithms():
    """深度学习算法演示函数"""
    # 创建检测器
    detector = DeepLearningCrackDetector()

    # 测试图像路径
    test_image_path = "test_images/sample_crack.jpg"

    if not os.path.exists(test_image_path):
        print(f"测试图像不存在: {test_image_path}")
        return

    # 读取测试图像
    image = cv2.imread(test_image_path)
    if image is None:
        print("无法读取测试图像")
        return

    print("=== 深度学习算法裂缝检测演示 ===")

    # 测试不同方法
    methods = ['classification', 'yolo', 'unet', 'ensemble']

    for method in methods:
        print(f"\n测试方法: {method}")

        try:
            result = detector.comprehensive_detection(image, method=method)

            if 'error' in result:
                print(f"检测失败: {result['error']}")
                continue

            # 显示统计信息
            stats = result['statistics']
            print(f"处理时间: {stats['processing_time']:.3f} 秒")

            if 'detection_count' in stats:
                print(f"检测数量: {stats['detection_count']}")
            elif 'patch_count' in stats:
                print(f"裂缝块数量: {stats['patch_count']}")
            elif 'crack_pixel_count' in stats:
                print(f"裂缝像素数: {stats['crack_pixel_count']}")

            # 可视化结果
            visualization = detector.visualize_results(
                image, result,
                save_path=f"output/deep_learning_{method}_result.jpg"
            )

        except Exception as e:
            print(f"{method} 检测异常: {e}")


if __name__ == "__main__":
    demo_deep_learning_algorithms()
