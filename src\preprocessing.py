import cv2
import numpy as np
import os

class Preprocessor:
    def __init__(self, image_path):
        self.image_path = image_path

    def read_image(self):
        """读取图像并转换为灰度"""
        gray_image = cv2.imread(self.image_path, 0)
        if gray_image is None:
            print(f"错误: 无法读取图片 {self.image_path}")
            return None
        return gray_image / 255.0

    def gaussian_blur(self, image, kernel_size, sigma):
        """高斯模糊"""
        return cv2.GaussianBlur(image, (kernel_size, kernel_size), sigma)

    def subtract_image(self, image, blur):
        """图像减法"""
        return cv2.subtract(image, blur)