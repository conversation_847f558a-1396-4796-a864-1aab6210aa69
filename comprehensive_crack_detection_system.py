# -*- coding: utf-8 -*-
"""
综合裂缝检测系统
整合所有算法模块，提供统一的接口和完整的检测流程

功能：
1. 算法集成 - 传统算法、深度学习、Transformer、像素标定
2. 批量处理 - 支持多图像批量检测
3. 性能对比 - 多算法结果对比分析
4. 完整流程 - 从图像输入到结果输出的完整流程
5. 报告生成 - 详细的检测报告和可视化结果
"""

import os
import cv2
import numpy as np
import time
import json
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import argparse
import matplotlib.pyplot as plt
from pathlib import Path

# 导入所有算法模块
from traditional_algorithms import Traditional<PERSON>rackDetector
from deep_learning_algorithms import DeepLearningCrackDetector
from transformer_models import TransformerCrackDetector
from pixel_calibration import PixelCalibrationCalculator, CameraCalibrator


class ComprehensiveCrackDetectionSystem:
    """综合裂缝检测系统"""
    
    def __init__(self, output_dir: str = "output", pixel_scale: float = 0.1):
        """
        初始化综合检测系统
        
        Args:
            output_dir: 输出目录
            pixel_scale: 像素比例 (mm/pixel)
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        (self.output_dir / "traditional").mkdir(exist_ok=True)
        (self.output_dir / "deep_learning").mkdir(exist_ok=True)
        (self.output_dir / "transformer").mkdir(exist_ok=True)
        (self.output_dir / "comparison").mkdir(exist_ok=True)
        (self.output_dir / "reports").mkdir(exist_ok=True)
        
        # 初始化检测器
        self.traditional_detector = TraditionalCrackDetector(debug_mode=True)
        self.deep_learning_detector = DeepLearningCrackDetector()
        self.transformer_detector = TransformerCrackDetector()
        
        # 初始化像素标定
        self.calibration_calculator = PixelCalibrationCalculator()
        self.calibration_calculator.set_pixel_scale_manual(pixel_scale)
        
        # 算法配置
        self.algorithm_configs = {
            'traditional': {
                'methods': ['otsu', 'kmeans', 'combined'],
                'params': {'enable_denoising': True, 'enable_morphology': True}
            },
            'deep_learning': {
                'methods': ['classification', 'yolo', 'unet', 'ensemble'],
                'params': {'confidence_threshold': 0.5}
            },
            'transformer': {
                'methods': ['vit', 'pctnet', 'ensemble'],
                'params': {'confidence_threshold': 0.5}
            }
        }
        
        # 结果存储
        self.detection_results = {}
        
    def detect_single_image(self, image_path: str, 
                           algorithms: List[str] = None,
                           save_results: bool = True) -> Dict:
        """
        单图像检测
        
        Args:
            image_path: 图像路径
            algorithms: 要使用的算法列表 ['traditional', 'deep_learning', 'transformer']
            save_results: 是否保存结果
            
        Returns:
            results: 检测结果字典
        """
        if algorithms is None:
            algorithms = ['traditional', 'deep_learning', 'transformer']
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        image_name = Path(image_path).stem
        results = {
            'image_path': image_path,
            'image_name': image_name,
            'image_size': image.shape[:2],
            'timestamp': datetime.now().isoformat(),
            'algorithms': {}
        }
        
        print(f"\n=== 检测图像: {image_name} ===")
        
        # 传统算法检测
        if 'traditional' in algorithms:
            print("运行传统算法...")
            traditional_results = self._run_traditional_algorithms(image)
            results['algorithms']['traditional'] = traditional_results
            
            if save_results:
                self._save_traditional_results(image, traditional_results, image_name)
        
        # 深度学习算法检测
        if 'deep_learning' in algorithms:
            print("运行深度学习算法...")
            dl_results = self._run_deep_learning_algorithms(image)
            results['algorithms']['deep_learning'] = dl_results
            
            if save_results:
                self._save_deep_learning_results(image, dl_results, image_name)
        
        # Transformer算法检测
        if 'transformer' in algorithms:
            print("运行Transformer算法...")
            transformer_results = self._run_transformer_algorithms(image)
            results['algorithms']['transformer'] = transformer_results
            
            if save_results:
                self._save_transformer_results(image, transformer_results, image_name)
        
        # 像素标定和尺寸计算
        print("计算物理尺寸...")
        results['measurements'] = self._calculate_measurements(results)
        
        # 性能对比
        results['comparison'] = self._compare_algorithms(results)
        
        if save_results:
            # 保存综合结果
            self._save_comprehensive_results(image, results, image_name)
            
            # 生成报告
            self._generate_report(results, image_name)
        
        return results
    
    def _run_traditional_algorithms(self, image: np.ndarray) -> Dict:
        """运行传统算法"""
        results = {}
        config = self.algorithm_configs['traditional']
        
        for method in config['methods']:
            try:
                start_time = time.time()
                result = self.traditional_detector.comprehensive_detection(
                    image, method=method, **config['params']
                )
                result['processing_time'] = time.time() - start_time
                results[method] = result
                
                print(f"  {method}: {result['statistics']['processing_time']:.3f}s")
                
            except Exception as e:
                print(f"  {method} 失败: {e}")
                results[method] = {'error': str(e)}
        
        return results
    
    def _run_deep_learning_algorithms(self, image: np.ndarray) -> Dict:
        """运行深度学习算法"""
        results = {}
        config = self.algorithm_configs['deep_learning']
        
        for method in config['methods']:
            try:
                start_time = time.time()
                result = self.deep_learning_detector.comprehensive_detection(
                    image, method=method, **config['params']
                )
                if 'error' not in result:
                    result['processing_time'] = time.time() - start_time
                results[method] = result
                
                if 'error' not in result:
                    print(f"  {method}: {result['statistics']['processing_time']:.3f}s")
                else:
                    print(f"  {method} 失败: {result['error']}")
                
            except Exception as e:
                print(f"  {method} 失败: {e}")
                results[method] = {'error': str(e)}
        
        return results
    
    def _run_transformer_algorithms(self, image: np.ndarray) -> Dict:
        """运行Transformer算法"""
        results = {}
        config = self.algorithm_configs['transformer']
        
        for method in config['methods']:
            try:
                start_time = time.time()
                result = self.transformer_detector.comprehensive_detection(
                    image, method=method, **config['params']
                )
                if 'error' not in result:
                    result['processing_time'] = time.time() - start_time
                results[method] = result
                
                if 'error' not in result:
                    print(f"  {method}: {result['statistics']['processing_time']:.3f}s")
                else:
                    print(f"  {method} 失败: {result['error']}")
                
            except Exception as e:
                print(f"  {method} 失败: {e}")
                results[method] = {'error': str(e)}
        
        return results
    
    def _calculate_measurements(self, results: Dict) -> Dict:
        """计算物理尺寸"""
        measurements = {}
        
        # 遍历所有算法结果
        for algorithm_type, algorithm_results in results['algorithms'].items():
            measurements[algorithm_type] = {}
            
            for method, result in algorithm_results.items():
                if 'error' in result or 'binary_mask' not in result:
                    continue
                
                try:
                    # 计算面积
                    area_info = self.calibration_calculator.calculate_crack_area(
                        result['binary_mask']
                    )
                    
                    # 提取骨架并计算长度
                    skeleton, skeleton_coords = self.calibration_calculator.extract_crack_skeleton(
                        result['binary_mask']
                    )
                    
                    if skeleton_coords:
                        length_info = self.calibration_calculator.calculate_crack_length(
                            skeleton_coords
                        )
                        
                        # 计算宽度
                        width_info = self.calibration_calculator.calculate_crack_width(
                            result['binary_mask'], skeleton_coords, 'distance_transform'
                        )
                        
                        measurements[algorithm_type][method] = {
                            'area': area_info,
                            'length': length_info,
                            'width': width_info
                        }
                    
                except Exception as e:
                    print(f"  {algorithm_type}-{method} 尺寸计算失败: {e}")
                    measurements[algorithm_type][method] = {'error': str(e)}
        
        return measurements
    
    def _compare_algorithms(self, results: Dict) -> Dict:
        """算法性能对比"""
        comparison = {
            'processing_times': {},
            'detection_accuracy': {},
            'crack_areas': {},
            'summary': {}
        }
        
        # 收集处理时间
        for algorithm_type, algorithm_results in results['algorithms'].items():
            comparison['processing_times'][algorithm_type] = {}
            comparison['crack_areas'][algorithm_type] = {}
            
            for method, result in algorithm_results.items():
                if 'error' not in result and 'processing_time' in result:
                    comparison['processing_times'][algorithm_type][method] = result['processing_time']
                
                # 收集裂缝面积信息
                if algorithm_type in results.get('measurements', {}):
                    if method in results['measurements'][algorithm_type]:
                        measurement = results['measurements'][algorithm_type][method]
                        if 'area' in measurement:
                            area_mm2 = measurement['area'].get('total_area_mm2', 0)
                            comparison['crack_areas'][algorithm_type][method] = area_mm2
        
        # 计算统计信息
        all_times = []
        all_areas = []
        
        for algorithm_times in comparison['processing_times'].values():
            all_times.extend(algorithm_times.values())
        
        for algorithm_areas in comparison['crack_areas'].values():
            all_areas.extend(algorithm_areas.values())
        
        if all_times:
            comparison['summary']['avg_processing_time'] = np.mean(all_times)
            comparison['summary']['fastest_algorithm'] = min(
                [(alg, method, time) for alg, methods in comparison['processing_times'].items() 
                 for method, time in methods.items()], 
                key=lambda x: x[2]
            )
        
        if all_areas:
            comparison['summary']['avg_crack_area'] = np.mean(all_areas)
            comparison['summary']['area_std'] = np.std(all_areas)
        
        return comparison

    def _save_traditional_results(self, image: np.ndarray, results: Dict, image_name: str):
        """保存传统算法结果"""
        for method, result in results.items():
            if 'error' in result:
                continue

            # 可视化结果
            visualization = self.traditional_detector.visualize_results(
                image, result,
                save_path=str(self.output_dir / "traditional" / f"{image_name}_{method}.jpg")
            )

            # 保存调试图像
            if 'debug_images' in result:
                debug_dir = self.output_dir / "traditional" / f"{image_name}_{method}_debug"
                debug_dir.mkdir(exist_ok=True)

                for debug_name, debug_image in result['debug_images'].items():
                    debug_path = debug_dir / f"{debug_name}.jpg"
                    cv2.imwrite(str(debug_path), debug_image)

    def _save_deep_learning_results(self, image: np.ndarray, results: Dict, image_name: str):
        """保存深度学习结果"""
        for method, result in results.items():
            if 'error' in result:
                continue

            # 可视化结果
            visualization = self.deep_learning_detector.visualize_results(
                image, result,
                save_path=str(self.output_dir / "deep_learning" / f"{image_name}_{method}.jpg")
            )

    def _save_transformer_results(self, image: np.ndarray, results: Dict, image_name: str):
        """保存Transformer结果"""
        for method, result in results.items():
            if 'error' in result:
                continue

            # 保存结果图像
            if 'binary_mask' in result:
                result_path = self.output_dir / "transformer" / f"{image_name}_{method}.jpg"
                cv2.imwrite(str(result_path), result['binary_mask'])

    def _save_comprehensive_results(self, image: np.ndarray, results: Dict, image_name: str):
        """保存综合结果"""
        # 创建对比图
        self._create_comparison_visualization(image, results, image_name)

        # 保存结果数据
        result_file = self.output_dir / "reports" / f"{image_name}_results.json"

        # 准备可序列化的数据
        serializable_results = self._make_serializable(results)

        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)

    def _create_comparison_visualization(self, image: np.ndarray, results: Dict, image_name: str):
        """创建对比可视化"""
        # 收集所有有效的二值掩码
        masks = {}

        for algorithm_type, algorithm_results in results['algorithms'].items():
            for method, result in algorithm_results.items():
                if 'error' not in result and 'binary_mask' in result:
                    masks[f"{algorithm_type}_{method}"] = result['binary_mask']

        if not masks:
            return

        # 创建网格布局
        n_masks = len(masks)
        cols = min(3, n_masks)
        rows = (n_masks + cols - 1) // cols

        fig, axes = plt.subplots(rows + 1, cols, figsize=(15, 5 * (rows + 1)))
        if rows == 0:
            axes = axes.reshape(1, -1)
        elif cols == 1:
            axes = axes.reshape(-1, 1)

        # 显示原始图像
        if len(image.shape) == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = image

        axes[0, 0].imshow(image_rgb)
        axes[0, 0].set_title("Original Image")
        axes[0, 0].axis('off')

        # 隐藏第一行的其他子图
        for i in range(1, cols):
            axes[0, i].axis('off')

        # 显示检测结果
        for idx, (name, mask) in enumerate(masks.items()):
            row = (idx // cols) + 1
            col = idx % cols

            axes[row, col].imshow(mask, cmap='gray')
            axes[row, col].set_title(name.replace('_', ' ').title())
            axes[row, col].axis('off')

        # 隐藏多余的子图
        total_plots = n_masks + 1
        for idx in range(total_plots, (rows + 1) * cols):
            row = idx // cols
            col = idx % cols
            axes[row, col].axis('off')

        plt.tight_layout()
        plt.savefig(self.output_dir / "comparison" / f"{image_name}_comparison.png",
                   dpi=150, bbox_inches='tight')
        plt.close()

    def _make_serializable(self, obj):
        """使对象可序列化"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.int32, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.float32, np.float64)):
            return float(obj)
        else:
            return obj

    def _generate_report(self, results: Dict, image_name: str):
        """生成检测报告"""
        report_content = f"""
# 裂缝检测报告

## 基本信息
- 图像名称: {image_name}
- 检测时间: {results['timestamp']}
- 图像尺寸: {results['image_size']}
- 像素比例: {self.calibration_calculator.pixel_scale:.6f} mm/pixel

## 算法性能对比
"""

        # 处理时间对比
        if 'processing_times' in results['comparison']:
            report_content += "\n### 处理时间对比\n"
            for algorithm_type, methods in results['comparison']['processing_times'].items():
                report_content += f"\n**{algorithm_type.title()}算法:**\n"
                for method, time in methods.items():
                    report_content += f"- {method}: {time:.3f}秒\n"

        # 检测结果对比
        if 'crack_areas' in results['comparison']:
            report_content += "\n### 检测面积对比\n"
            for algorithm_type, methods in results['comparison']['crack_areas'].items():
                report_content += f"\n**{algorithm_type.title()}算法:**\n"
                for method, area in methods.items():
                    report_content += f"- {method}: {area:.2f} mm²\n"

        # 详细测量结果
        if 'measurements' in results:
            report_content += "\n## 详细测量结果\n"
            for algorithm_type, algorithm_measurements in results['measurements'].items():
                report_content += f"\n### {algorithm_type.title()}算法\n"
                for method, measurement in algorithm_measurements.items():
                    if 'error' in measurement:
                        continue

                    report_content += f"\n**{method}方法:**\n"

                    if 'area' in measurement:
                        area_info = measurement['area']
                        report_content += f"- 总面积: {area_info.get('total_area_mm2', 0):.2f} mm²\n"
                        report_content += f"- 裂缝数量: {area_info.get('crack_count', 0)}\n"

                    if 'length' in measurement:
                        length_info = measurement['length']
                        report_content += f"- 总长度: {length_info.get('total_length_mm', 0):.2f} mm\n"

                    if 'width' in measurement and 'overall_mean_width_mm' in measurement['width']:
                        width_info = measurement['width']
                        report_content += f"- 平均宽度: {width_info.get('overall_mean_width_mm', 0):.3f} mm\n"
                        report_content += f"- 最大宽度: {width_info.get('overall_max_width_mm', 0):.3f} mm\n"

        # 保存报告
        report_file = self.output_dir / "reports" / f"{image_name}_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

    def batch_detection(self, image_dir: str,
                       algorithms: List[str] = None,
                       image_extensions: List[str] = None) -> Dict:
        """
        批量检测

        Args:
            image_dir: 图像目录
            algorithms: 算法列表
            image_extensions: 支持的图像扩展名

        Returns:
            batch_results: 批量检测结果
        """
        if image_extensions is None:
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']

        image_dir = Path(image_dir)
        if not image_dir.exists():
            raise ValueError(f"图像目录不存在: {image_dir}")

        # 查找图像文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(image_dir.glob(f"*{ext}"))
            image_files.extend(image_dir.glob(f"*{ext.upper()}"))

        if not image_files:
            raise ValueError(f"在目录 {image_dir} 中未找到图像文件")

        print(f"找到 {len(image_files)} 个图像文件")

        batch_results = {
            'total_images': len(image_files),
            'processed_images': 0,
            'failed_images': 0,
            'results': {},
            'summary': {}
        }

        # 逐个处理图像
        for i, image_file in enumerate(image_files):
            print(f"\n处理进度: {i+1}/{len(image_files)}")

            try:
                result = self.detect_single_image(
                    str(image_file),
                    algorithms=algorithms,
                    save_results=True
                )

                batch_results['results'][image_file.name] = result
                batch_results['processed_images'] += 1

            except Exception as e:
                print(f"处理图像 {image_file.name} 失败: {e}")
                batch_results['failed_images'] += 1

        # 生成批量处理摘要
        self._generate_batch_summary(batch_results)

        return batch_results

    def _generate_batch_summary(self, batch_results: Dict):
        """生成批量处理摘要"""
        summary_content = f"""
# 批量检测摘要报告

## 处理统计
- 总图像数: {batch_results['total_images']}
- 成功处理: {batch_results['processed_images']}
- 处理失败: {batch_results['failed_images']}
- 成功率: {batch_results['processed_images']/batch_results['total_images']*100:.1f}%

## 算法性能统计
"""

        # 收集所有处理时间
        all_times = {}
        all_areas = {}

        for image_name, result in batch_results['results'].items():
            if 'comparison' in result and 'processing_times' in result['comparison']:
                for algorithm_type, methods in result['comparison']['processing_times'].items():
                    if algorithm_type not in all_times:
                        all_times[algorithm_type] = {}

                    for method, time in methods.items():
                        if method not in all_times[algorithm_type]:
                            all_times[algorithm_type][method] = []
                        all_times[algorithm_type][method].append(time)

            if 'comparison' in result and 'crack_areas' in result['comparison']:
                for algorithm_type, methods in result['comparison']['crack_areas'].items():
                    if algorithm_type not in all_areas:
                        all_areas[algorithm_type] = {}

                    for method, area in methods.items():
                        if method not in all_areas[algorithm_type]:
                            all_areas[algorithm_type][method] = []
                        all_areas[algorithm_type][method].append(area)

        # 计算平均处理时间
        summary_content += "\n### 平均处理时间\n"
        for algorithm_type, methods in all_times.items():
            summary_content += f"\n**{algorithm_type.title()}:**\n"
            for method, times in methods.items():
                avg_time = np.mean(times)
                std_time = np.std(times)
                summary_content += f"- {method}: {avg_time:.3f}±{std_time:.3f}秒\n"

        # 计算平均检测面积
        summary_content += "\n### 平均检测面积\n"
        for algorithm_type, methods in all_areas.items():
            summary_content += f"\n**{algorithm_type.title()}:**\n"
            for method, areas in methods.items():
                avg_area = np.mean(areas)
                std_area = np.std(areas)
                summary_content += f"- {method}: {avg_area:.2f}±{std_area:.2f} mm²\n"

        # 保存摘要
        summary_file = self.output_dir / "reports" / "batch_summary.md"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary_content)

        print(f"\n批量处理完成！摘要报告已保存到: {summary_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="综合裂缝检测系统")
    parser.add_argument("--input", "-i", required=True,
                       help="输入图像文件或目录")
    parser.add_argument("--output", "-o", default="output",
                       help="输出目录")
    parser.add_argument("--algorithms", "-a", nargs="+",
                       choices=['traditional', 'deep_learning', 'transformer'],
                       default=['traditional', 'deep_learning', 'transformer'],
                       help="要使用的算法")
    parser.add_argument("--pixel-scale", "-p", type=float, default=0.1,
                       help="像素比例 (mm/pixel)")
    parser.add_argument("--batch", "-b", action="store_true",
                       help="批量处理模式")

    args = parser.parse_args()

    # 创建检测系统
    system = ComprehensiveCrackDetectionSystem(
        output_dir=args.output,
        pixel_scale=args.pixel_scale
    )

    try:
        if args.batch or os.path.isdir(args.input):
            # 批量处理
            print("开始批量检测...")
            results = system.batch_detection(args.input, args.algorithms)
            print(f"批量检测完成！处理了 {results['processed_images']} 个图像")
        else:
            # 单图像处理
            print("开始单图像检测...")
            result = system.detect_single_image(args.input, args.algorithms)
            print("单图像检测完成！")

    except Exception as e:
        print(f"检测过程中发生错误: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
