# -*- coding: utf-8 -*-
"""
像素标定算法模块
实现裂缝尺寸量化，将像素距离转换为实际物理尺寸

功能：
1. 相机标定 - 获取相机内参和畸变系数
2. 像素比例计算 - 建立像素-实际距离映射关系
3. 裂缝特征提取 - 提取骨架和轮廓边界
4. 尺寸计算 - 计算裂缝长度、宽度、面积等参数
5. Freeman编码 - 记录裂缝轮廓坐标
"""

import cv2
import numpy as np
from skimage.morphology import skeletonize, medial_axis
from skimage import measure
from scipy.spatial.distance import cdist
from typing import Tuple, List, Dict, Optional, Union
import math
import json
import os
from datetime import datetime


class CameraCalibrator:
    """相机标定类"""
    
    def __init__(self):
        """初始化相机标定器"""
        self.camera_matrix = None
        self.dist_coeffs = None
        self.calibration_data = {}
        
    def calibrate_camera(self, calibration_images: List[str], 
                        chessboard_size: Tuple[int, int] = (9, 6),
                        square_size: float = 25.0) -> bool:
        """
        相机标定
        
        Args:
            calibration_images: 标定图像路径列表
            chessboard_size: 棋盘格内角点数量 (width, height)
            square_size: 棋盘格方格实际尺寸 (mm)
            
        Returns:
            success: 标定是否成功
        """
        # 准备对象点
        objp = np.zeros((chessboard_size[0] * chessboard_size[1], 3), np.float32)
        objp[:, :2] = np.mgrid[0:chessboard_size[0], 0:chessboard_size[1]].T.reshape(-1, 2)
        objp *= square_size
        
        # 存储对象点和图像点
        objpoints = []  # 3D点
        imgpoints = []  # 2D点
        
        valid_images = 0
        
        for img_path in calibration_images:
            if not os.path.exists(img_path):
                continue
                
            img = cv2.imread(img_path)
            if img is None:
                continue
                
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 寻找棋盘格角点
            ret, corners = cv2.findChessboardCorners(gray, chessboard_size, None)
            
            if ret:
                objpoints.append(objp)
                
                # 亚像素精度优化
                corners2 = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1),
                                          (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001))
                imgpoints.append(corners2)
                valid_images += 1
        
        if valid_images < 3:
            print(f"有效标定图像太少: {valid_images}")
            return False
        
        # 执行标定
        img_shape = gray.shape[::-1]
        ret, camera_matrix, dist_coeffs, rvecs, tvecs = cv2.calibrateCamera(
            objpoints, imgpoints, img_shape, None, None
        )
        
        if ret:
            self.camera_matrix = camera_matrix
            self.dist_coeffs = dist_coeffs
            
            # 计算重投影误差
            total_error = 0
            for i in range(len(objpoints)):
                imgpoints2, _ = cv2.projectPoints(objpoints[i], rvecs[i], tvecs[i], 
                                                camera_matrix, dist_coeffs)
                error = cv2.norm(imgpoints[i], imgpoints2, cv2.NORM_L2) / len(imgpoints2)
                total_error += error
            
            mean_error = total_error / len(objpoints)
            
            self.calibration_data = {
                'camera_matrix': camera_matrix.tolist(),
                'dist_coeffs': dist_coeffs.tolist(),
                'image_size': img_shape,
                'valid_images': valid_images,
                'mean_reprojection_error': mean_error,
                'calibration_date': datetime.now().isoformat()
            }
            
            print(f"相机标定成功！重投影误差: {mean_error:.4f} 像素")
            return True
        else:
            print("相机标定失败")
            return False
    
    def save_calibration(self, filepath: str):
        """保存标定结果"""
        if self.calibration_data:
            with open(filepath, 'w') as f:
                json.dump(self.calibration_data, f, indent=2)
            print(f"标定结果已保存到: {filepath}")
    
    def load_calibration(self, filepath: str) -> bool:
        """加载标定结果"""
        try:
            with open(filepath, 'r') as f:
                self.calibration_data = json.load(f)
            
            self.camera_matrix = np.array(self.calibration_data['camera_matrix'])
            self.dist_coeffs = np.array(self.calibration_data['dist_coeffs'])
            
            print(f"标定结果已加载: {filepath}")
            return True
        except Exception as e:
            print(f"加载标定结果失败: {e}")
            return False


class PixelCalibrationCalculator:
    """像素标定计算器"""
    
    def __init__(self, camera_calibrator: Optional[CameraCalibrator] = None):
        """
        初始化像素标定计算器
        
        Args:
            camera_calibrator: 相机标定器实例
        """
        self.camera_calibrator = camera_calibrator
        self.pixel_scale = None  # mm/pixel
        
    def calculate_pixel_scale(self, object_distance: float, 
                            sensor_width: float = 23.6,
                            image_width: int = 1920,
                            focal_length: float = None) -> float:
        """
        计算像素比例
        公式: s = (d_w * l) / (P * f)
        
        Args:
            object_distance: 物距 (mm)
            sensor_width: 传感器宽度 (mm)
            image_width: 图像宽度 (pixels)
            focal_length: 焦距 (mm)，如果None则从相机标定获取
            
        Returns:
            pixel_scale: 像素比例 (mm/pixel)
        """
        if focal_length is None:
            if self.camera_calibrator and self.camera_calibrator.camera_matrix is not None:
                # 从相机标定获取焦距
                focal_length = self.camera_calibrator.camera_matrix[0, 0]  # fx
                # 将像素焦距转换为物理焦距
                focal_length = focal_length * sensor_width / image_width
            else:
                raise ValueError("需要提供焦距或相机标定信息")
        
        # 计算像素比例
        self.pixel_scale = (object_distance * sensor_width) / (image_width * focal_length)
        
        print(f"像素比例: {self.pixel_scale:.6f} mm/pixel")
        return self.pixel_scale
    
    def set_pixel_scale_manual(self, pixel_scale: float):
        """手动设置像素比例"""
        self.pixel_scale = pixel_scale
        print(f"手动设置像素比例: {self.pixel_scale:.6f} mm/pixel")
    
    def extract_crack_skeleton(self, binary_image: np.ndarray) -> Tuple[np.ndarray, List[np.ndarray]]:
        """
        提取裂缝骨架
        
        Args:
            binary_image: 二值化裂缝图像
            
        Returns:
            skeleton: 骨架图像
            skeleton_coords: 骨架坐标列表
        """
        # 确保是二值图像
        if len(binary_image.shape) == 3:
            binary_image = cv2.cvtColor(binary_image, cv2.COLOR_BGR2GRAY)
        
        binary_image = (binary_image > 127).astype(bool)
        
        # 提取骨架
        skeleton = skeletonize(binary_image)
        
        # 获取连通的骨架组件
        labeled_skeleton = measure.label(skeleton)
        skeleton_coords = []
        
        for region in measure.regionprops(labeled_skeleton):
            coords = region.coords
            # 按照连通性排序坐标点
            sorted_coords = self._sort_skeleton_coords(coords)
            skeleton_coords.append(sorted_coords)
        
        return skeleton.astype(np.uint8) * 255, skeleton_coords
    
    def _sort_skeleton_coords(self, coords: np.ndarray) -> np.ndarray:
        """
        对骨架坐标点进行排序，使其按照连通性排列
        
        Args:
            coords: 骨架坐标点
            
        Returns:
            sorted_coords: 排序后的坐标点
        """
        if len(coords) <= 2:
            return coords
        
        # 构建距离矩阵
        distances = cdist(coords, coords)
        
        # 寻找端点（只有一个邻居的点）
        neighbors = np.sum(distances <= np.sqrt(2), axis=1) - 1  # 减去自己
        endpoints = np.where(neighbors == 1)[0]
        
        if len(endpoints) == 0:
            # 闭合曲线，从第一个点开始
            start_idx = 0
        else:
            # 从端点开始
            start_idx = endpoints[0]
        
        # 贪心算法构建路径
        sorted_indices = [start_idx]
        visited = {start_idx}
        current = start_idx
        
        while len(sorted_indices) < len(coords):
            # 找到最近的未访问邻居
            distances_from_current = distances[current]
            valid_neighbors = []
            
            for i, dist in enumerate(distances_from_current):
                if i not in visited and dist <= np.sqrt(2):
                    valid_neighbors.append((i, dist))
            
            if not valid_neighbors:
                break
            
            # 选择最近的邻居
            next_idx = min(valid_neighbors, key=lambda x: x[1])[0]
            sorted_indices.append(next_idx)
            visited.add(next_idx)
            current = next_idx
        
        return coords[sorted_indices]
    
    def calculate_crack_length(self, skeleton_coords: List[np.ndarray]) -> Dict:
        """
        计算裂缝长度
        
        Args:
            skeleton_coords: 骨架坐标列表
            
        Returns:
            length_info: 长度信息字典
        """
        if self.pixel_scale is None:
            raise ValueError("请先设置像素比例")
        
        total_length_pixels = 0
        crack_lengths = []
        
        for coords in skeleton_coords:
            if len(coords) < 2:
                continue
            
            # 计算相邻点之间的距离
            length_pixels = 0
            for i in range(len(coords) - 1):
                p1 = coords[i]
                p2 = coords[i + 1]
                dist = np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                length_pixels += dist
            
            total_length_pixels += length_pixels
            crack_lengths.append({
                'length_pixels': length_pixels,
                'length_mm': length_pixels * self.pixel_scale,
                'point_count': len(coords)
            })
        
        total_length_mm = total_length_pixels * self.pixel_scale
        
        return {
            'total_length_pixels': total_length_pixels,
            'total_length_mm': total_length_mm,
            'crack_count': len(crack_lengths),
            'individual_cracks': crack_lengths,
            'pixel_scale': self.pixel_scale
        }
    
    def calculate_crack_width(self, binary_image: np.ndarray, 
                            skeleton_coords: List[np.ndarray],
                            method: str = 'normal_distance') -> Dict:
        """
        计算裂缝宽度
        
        Args:
            binary_image: 二值化裂缝图像
            skeleton_coords: 骨架坐标列表
            method: 计算方法 ('normal_distance', 'distance_transform')
            
        Returns:
            width_info: 宽度信息字典
        """
        if self.pixel_scale is None:
            raise ValueError("请先设置像素比例")
        
        if method == 'normal_distance':
            return self._calculate_width_normal_distance(binary_image, skeleton_coords)
        elif method == 'distance_transform':
            return self._calculate_width_distance_transform(binary_image, skeleton_coords)
        else:
            raise ValueError(f"不支持的宽度计算方法: {method}")
    
    def _calculate_width_normal_distance(self, binary_image: np.ndarray, 
                                       skeleton_coords: List[np.ndarray]) -> Dict:
        """
        使用法线距离法计算宽度
        
        Args:
            binary_image: 二值化图像
            skeleton_coords: 骨架坐标
            
        Returns:
            width_info: 宽度信息
        """
        # 确保是二值图像
        if len(binary_image.shape) == 3:
            binary_image = cv2.cvtColor(binary_image, cv2.COLOR_BGR2GRAY)
        
        binary_image = (binary_image > 127).astype(np.uint8)
        
        # 找到裂缝边界
        contours, _ = cv2.findContours(binary_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        all_widths = []
        crack_widths = []
        
        for coords in skeleton_coords:
            if len(coords) < 3:
                continue
            
            widths_for_crack = []
            
            for i in range(1, len(coords) - 1):
                # 计算法线方向
                p_prev = coords[i - 1]
                p_curr = coords[i]
                p_next = coords[i + 1]
                
                # 切线方向
                tangent = np.array([p_next[1] - p_prev[1], p_next[0] - p_prev[0]], dtype=np.float32)
                tangent_norm = np.linalg.norm(tangent)
                
                if tangent_norm == 0:
                    continue
                
                tangent = tangent / tangent_norm
                
                # 法线方向（垂直于切线）
                normal = np.array([-tangent[1], tangent[0]])
                
                # 沿法线方向寻找边界交点
                width_pixels = self._find_width_along_normal(binary_image, p_curr, normal)
                
                if width_pixels > 0:
                    width_mm = width_pixels * self.pixel_scale
                    widths_for_crack.append(width_pixels)
                    all_widths.append(width_mm)
            
            if widths_for_crack:
                crack_widths.append({
                    'widths_pixels': widths_for_crack,
                    'widths_mm': [w * self.pixel_scale for w in widths_for_crack],
                    'mean_width_pixels': np.mean(widths_for_crack),
                    'mean_width_mm': np.mean(widths_for_crack) * self.pixel_scale,
                    'max_width_pixels': np.max(widths_for_crack),
                    'max_width_mm': np.max(widths_for_crack) * self.pixel_scale
                })
        
        if all_widths:
            return {
                'overall_mean_width_mm': np.mean(all_widths),
                'overall_max_width_mm': np.max(all_widths),
                'overall_min_width_mm': np.min(all_widths),
                'width_std_mm': np.std(all_widths),
                'individual_cracks': crack_widths,
                'pixel_scale': self.pixel_scale,
                'method': 'normal_distance'
            }
        else:
            return {
                'error': '无法计算宽度',
                'method': 'normal_distance'
            }
    
    def _find_width_along_normal(self, binary_image: np.ndarray, 
                               center_point: np.ndarray, 
                               normal: np.ndarray,
                               max_search_distance: int = 50) -> float:
        """
        沿法线方向寻找裂缝宽度
        
        Args:
            binary_image: 二值图像
            center_point: 中心点（骨架点）
            normal: 法线方向向量
            max_search_distance: 最大搜索距离
            
        Returns:
            width: 宽度（像素）
        """
        h, w = binary_image.shape
        y, x = center_point
        
        # 沿正负法线方向搜索边界
        boundaries = []
        
        for direction in [1, -1]:
            for distance in range(1, max_search_distance):
                search_x = int(x + direction * distance * normal[0])
                search_y = int(y + direction * distance * normal[1])
                
                # 检查边界
                if search_x < 0 or search_x >= w or search_y < 0 or search_y >= h:
                    break
                
                # 如果到达背景（非裂缝区域）
                if binary_image[search_y, search_x] == 0:
                    boundaries.append(np.array([search_y, search_x]))
                    break
        
        # 计算宽度
        if len(boundaries) == 2:
            width = np.linalg.norm(boundaries[0] - boundaries[1])
            return width
        else:
            return 0.0

    def _calculate_width_distance_transform(self, binary_image: np.ndarray,
                                          skeleton_coords: List[np.ndarray]) -> Dict:
        """
        使用距离变换法计算宽度

        Args:
            binary_image: 二值化图像
            skeleton_coords: 骨架坐标

        Returns:
            width_info: 宽度信息
        """
        # 确保是二值图像
        if len(binary_image.shape) == 3:
            binary_image = cv2.cvtColor(binary_image, cv2.COLOR_BGR2GRAY)

        binary_image = (binary_image > 127).astype(np.uint8)

        # 计算距离变换
        dist_transform = cv2.distanceTransform(binary_image, cv2.DIST_L2, 5)

        all_widths = []
        crack_widths = []

        for coords in skeleton_coords:
            widths_for_crack = []

            for coord in coords:
                y, x = coord
                # 距离变换值的2倍即为该点的宽度
                width_pixels = dist_transform[y, x] * 2

                if width_pixels > 0:
                    widths_for_crack.append(width_pixels)
                    all_widths.append(width_pixels * self.pixel_scale)

            if widths_for_crack:
                crack_widths.append({
                    'widths_pixels': widths_for_crack,
                    'widths_mm': [w * self.pixel_scale for w in widths_for_crack],
                    'mean_width_pixels': np.mean(widths_for_crack),
                    'mean_width_mm': np.mean(widths_for_crack) * self.pixel_scale,
                    'max_width_pixels': np.max(widths_for_crack),
                    'max_width_mm': np.max(widths_for_crack) * self.pixel_scale
                })

        if all_widths:
            return {
                'overall_mean_width_mm': np.mean(all_widths),
                'overall_max_width_mm': np.max(all_widths),
                'overall_min_width_mm': np.min(all_widths),
                'width_std_mm': np.std(all_widths),
                'individual_cracks': crack_widths,
                'pixel_scale': self.pixel_scale,
                'method': 'distance_transform'
            }
        else:
            return {
                'error': '无法计算宽度',
                'method': 'distance_transform'
            }

    def calculate_crack_area(self, binary_image: np.ndarray) -> Dict:
        """
        计算裂缝面积

        Args:
            binary_image: 二值化裂缝图像

        Returns:
            area_info: 面积信息字典
        """
        if self.pixel_scale is None:
            raise ValueError("请先设置像素比例")

        # 确保是二值图像
        if len(binary_image.shape) == 3:
            binary_image = cv2.cvtColor(binary_image, cv2.COLOR_BGR2GRAY)

        # 计算裂缝像素数
        crack_pixels = np.sum(binary_image > 127)

        # 转换为实际面积
        pixel_area_mm2 = self.pixel_scale ** 2
        crack_area_mm2 = crack_pixels * pixel_area_mm2

        # 分析连通域
        labeled_image = measure.label(binary_image > 127)
        regions = measure.regionprops(labeled_image)

        individual_areas = []
        for region in regions:
            area_pixels = region.area
            area_mm2 = area_pixels * pixel_area_mm2
            individual_areas.append({
                'area_pixels': area_pixels,
                'area_mm2': area_mm2,
                'centroid': region.centroid
            })

        return {
            'total_area_pixels': crack_pixels,
            'total_area_mm2': crack_area_mm2,
            'crack_count': len(individual_areas),
            'individual_areas': individual_areas,
            'pixel_scale': self.pixel_scale,
            'pixel_area_mm2': pixel_area_mm2
        }

    def comprehensive_measurement(self, binary_image: np.ndarray) -> Dict:
        """
        综合测量：计算裂缝的所有几何参数

        Args:
            binary_image: 二值化裂缝图像

        Returns:
            measurement_result: 综合测量结果
        """
        if self.pixel_scale is None:
            raise ValueError("请先设置像素比例")

        start_time = datetime.now()

        # 提取骨架
        skeleton, skeleton_coords = self.extract_crack_skeleton(binary_image)

        # 计算长度
        length_info = self.calculate_crack_length(skeleton_coords)

        # 计算宽度（使用两种方法）
        width_info_normal = self.calculate_crack_width(binary_image, skeleton_coords, 'normal_distance')
        width_info_dist = self.calculate_crack_width(binary_image, skeleton_coords, 'distance_transform')

        # 计算面积
        area_info = self.calculate_crack_area(binary_image)

        # 计算形态学特征
        morphological_features = self._calculate_morphological_features(binary_image, skeleton_coords)

        processing_time = (datetime.now() - start_time).total_seconds()

        result = {
            'measurement_summary': {
                'total_length_mm': length_info['total_length_mm'],
                'total_area_mm2': area_info['total_area_mm2'],
                'mean_width_mm': width_info_normal.get('overall_mean_width_mm', 0),
                'max_width_mm': width_info_normal.get('overall_max_width_mm', 0),
                'crack_count': length_info['crack_count'],
                'pixel_scale': self.pixel_scale,
                'processing_time': processing_time
            },
            'detailed_measurements': {
                'length': length_info,
                'width_normal': width_info_normal,
                'width_distance_transform': width_info_dist,
                'area': area_info,
                'morphological': morphological_features
            },
            'skeleton_image': skeleton,
            'skeleton_coords': skeleton_coords
        }

        return result

    def _calculate_morphological_features(self, binary_image: np.ndarray,
                                        skeleton_coords: List[np.ndarray]) -> Dict:
        """
        计算形态学特征

        Args:
            binary_image: 二值化图像
            skeleton_coords: 骨架坐标

        Returns:
            features: 形态学特征字典
        """
        # 连通域分析
        labeled_image = measure.label(binary_image > 127)
        regions = measure.regionprops(labeled_image)

        features = {
            'crack_features': []
        }

        for i, region in enumerate(regions):
            # 基本几何特征
            area = region.area * (self.pixel_scale ** 2)
            perimeter = region.perimeter * self.pixel_scale

            # 长宽比
            bbox = region.bbox
            width = (bbox[3] - bbox[1]) * self.pixel_scale
            height = (bbox[2] - bbox[0]) * self.pixel_scale
            aspect_ratio = max(width, height) / min(width, height) if min(width, height) > 0 else 0

            # 圆形度
            circularity = 4 * np.pi * region.area / (region.perimeter ** 2) if region.perimeter > 0 else 0

            # 实体度
            solidity = region.solidity

            # 方向角
            orientation = region.orientation * 180 / np.pi

            # 粗糙度（周长与凸包周长的比值）
            convex_perimeter = region.perimeter_crofton if hasattr(region, 'perimeter_crofton') else region.perimeter
            roughness = region.perimeter / convex_perimeter if convex_perimeter > 0 else 1

            crack_feature = {
                'crack_id': i,
                'area_mm2': area,
                'perimeter_mm': perimeter,
                'width_mm': width,
                'height_mm': height,
                'aspect_ratio': aspect_ratio,
                'circularity': circularity,
                'solidity': solidity,
                'orientation_degrees': orientation,
                'roughness': roughness,
                'centroid': region.centroid
            }

            features['crack_features'].append(crack_feature)

        return features

    def visualize_measurements(self, original_image: np.ndarray,
                             measurement_result: Dict,
                             save_path: Optional[str] = None) -> np.ndarray:
        """
        可视化测量结果

        Args:
            original_image: 原始图像
            measurement_result: 测量结果
            save_path: 保存路径

        Returns:
            visualization: 可视化图像
        """
        # 创建可视化图像
        if len(original_image.shape) == 3:
            visualization = original_image.copy()
        else:
            visualization = cv2.cvtColor(original_image, cv2.COLOR_GRAY2BGR)

        # 绘制骨架
        skeleton = measurement_result['skeleton_image']
        skeleton_colored = np.zeros_like(visualization)
        skeleton_colored[:, :, 1] = skeleton  # 绿色通道

        # 叠加骨架
        alpha = 0.7
        visualization = cv2.addWeighted(visualization, 1-alpha, skeleton_colored, alpha, 0)

        # 添加测量信息
        summary = measurement_result['measurement_summary']

        info_text = [
            f"Total Length: {summary['total_length_mm']:.2f} mm",
            f"Total Area: {summary['total_area_mm2']:.2f} mm²",
            f"Mean Width: {summary['mean_width_mm']:.3f} mm",
            f"Max Width: {summary['max_width_mm']:.3f} mm",
            f"Crack Count: {summary['crack_count']}",
            f"Scale: {summary['pixel_scale']:.6f} mm/pixel"
        ]

        # 绘制文本背景
        text_bg_height = len(info_text) * 25 + 20
        cv2.rectangle(visualization, (10, 10), (400, text_bg_height), (0, 0, 0), -1)
        cv2.rectangle(visualization, (10, 10), (400, text_bg_height), (255, 255, 255), 2)

        # 绘制文本
        for i, text in enumerate(info_text):
            cv2.putText(visualization, text, (15, 35 + i * 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

        # 保存结果
        if save_path:
            cv2.imwrite(save_path, visualization)

        return visualization


def demo_pixel_calibration():
    """像素标定算法演示函数"""
    print("=== 像素标定算法演示 ===")

    # 创建相机标定器
    calibrator = CameraCalibrator()

    # 创建像素标定计算器
    calculator = PixelCalibrationCalculator(calibrator)

    # 手动设置像素比例（实际应用中应通过相机标定获得）
    calculator.set_pixel_scale_manual(0.1)  # 0.1 mm/pixel

    # 测试图像路径
    test_image_path = "test_images/sample_crack.jpg"

    if not os.path.exists(test_image_path):
        print(f"测试图像不存在: {test_image_path}")
        return

    # 读取测试图像
    image = cv2.imread(test_image_path, cv2.IMREAD_GRAYSCALE)
    if image is None:
        print("无法读取测试图像")
        return

    # 简单二值化处理
    _, binary_image = cv2.threshold(image, 127, 255, cv2.THRESH_BINARY_INV)

    try:
        # 执行综合测量
        result = calculator.comprehensive_measurement(binary_image)

        # 显示结果
        summary = result['measurement_summary']
        print(f"\n测量结果:")
        print(f"总长度: {summary['total_length_mm']:.2f} mm")
        print(f"总面积: {summary['total_area_mm2']:.2f} mm²")
        print(f"平均宽度: {summary['mean_width_mm']:.3f} mm")
        print(f"最大宽度: {summary['max_width_mm']:.3f} mm")
        print(f"裂缝数量: {summary['crack_count']}")
        print(f"处理时间: {summary['processing_time']:.3f} 秒")

        # 可视化结果
        original_color = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
        visualization = calculator.visualize_measurements(
            original_color, result,
            save_path="output/pixel_calibration_result.jpg"
        )

        print("\n可视化结果已保存到: output/pixel_calibration_result.jpg")

    except Exception as e:
        print(f"测量失败: {e}")


if __name__ == "__main__":
    demo_pixel_calibration()
