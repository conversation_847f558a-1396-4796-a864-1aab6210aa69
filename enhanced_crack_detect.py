#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版裂缝检测命令行工具

使用方法:
1. 单张图片检测: python enhanced_crack_detect.py --source image.jpg --scale 0.1
2. 文件夹批量检测: python enhanced_crack_detect.py --source folder_path --scale 0.1 --batch
3. 带宽度计算: python enhanced_crack_detect.py --source image.jpg --scale 0.1 --width
4. 使用YOLO模型: python enhanced_crack_detect.py --source image.jpg --yolo yolo11n-seg.pt

功能特点:
- 集成YOLO分割模型和传统图像处理
- 多层次图像预处理
- 智能轮廓筛选
- 精确面积计算
- 裂缝宽度计算
- CSV报告生成
"""

import argparse
import os
import sys
import time
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from crack_detection import EnhancedCrackDetector
from crack_width_calculator import CrackWidthCalculator
import cv2
import numpy as np


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="增强版裂缝检测系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s --source test.jpg --scale 0.1 --unit mm²
  %(prog)s --source images/ --batch --scale 0.05 --yolo yolo11n-seg.pt
  %(prog)s --source crack.jpg --width --scale 0.1 --show
        """
    )
    
    # 必需参数
    parser.add_argument('--source', type=str, required=False,
                       help='输入图像文件或文件夹路径')
    
    # 检测参数
    parser.add_argument('--scale', type=float, default=0.1,
                       help='比例尺 (像素/实际单位), 默认: 0.1')
    parser.add_argument('--unit', type=str, default='mm²',
                       choices=['mm²', 'cm²', 'm²', '像素²'],
                       help='面积单位, 默认: mm²')
    
    # 模型参数
    parser.add_argument('--yolo', type=str, default=None,
                       help='YOLO分割模型路径')
    parser.add_argument('--model-id', type=str, default=None,
                       help='YOLO模型ID (通过模型管理器选择)')
    parser.add_argument('--list-models', action='store_true',
                       help='列出所有可用模型')
    parser.add_argument('--download-model', type=str, default=None,
                       help='下载指定模型')
    parser.add_argument('--no-yolo', action='store_true',
                       help='禁用YOLO模型，仅使用传统方法')
    
    # 功能选项
    parser.add_argument('--batch', action='store_true',
                       help='批量处理模式')
    parser.add_argument('--width', action='store_true',
                       help='计算裂缝宽度')
    parser.add_argument('--show', action='store_true',
                       help='显示检测结果')
    parser.add_argument('--save', action='store_true', default=True,
                       help='保存检测结果 (默认开启)')
    parser.add_argument('--no-save', action='store_false', dest='save',
                       help='不保存检测结果')

    # 专业检测选项
    parser.add_argument('--pavement', action='store_true',
                       help='使用专业路面裂缝检测 (Canny + Gabor)')
    parser.add_argument('--concrete', action='store_true',
                       help='使用专业混凝土裂缝检测 (完整长度/宽度/面积计算)')
    parser.add_argument('--detection-method', type=str,
                       choices=['standard', 'pavement', 'concrete', 'auto'],
                       default='standard',
                       help='检测方法选择: standard(标准), pavement(路面), concrete(混凝土), auto(自动选择)')
    
    # 输出参数
    parser.add_argument('--output', type=str, default='output',
                       help='输出目录, 默认: output')
    parser.add_argument('--csv', action='store_true', default=True,
                       help='生成CSV报告 (默认开启)')
    parser.add_argument('--no-csv', action='store_false', dest='csv',
                       help='不生成CSV报告')
    
    # 筛选参数
    parser.add_argument('--min-area', type=int, default=50,
                       help='最小轮廓面积 (像素²), 默认: 50')
    parser.add_argument('--max-area', type=int, default=50000,
                       help='最大轮廓面积 (像素²), 默认: 50000')
    parser.add_argument('--min-circularity', type=float, default=0.1,
                       help='最小圆形度, 默认: 0.1')
    parser.add_argument('--max-circularity', type=float, default=0.9,
                       help='最大圆形度, 默认: 0.9')
    
    # 调试选项
    parser.add_argument('--debug', action='store_true',
                       help='保存调试图像')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')
    
    return parser.parse_args()


def setup_detector(args):
    """设置检测器"""
    # 确定YOLO模型参数
    yolo_model_path = None
    yolo_model_id = None

    if not args.no_yolo:
        if args.yolo:
            yolo_model_path = args.yolo
        elif args.model_id:
            yolo_model_id = args.model_id
        # 如果都没指定，让检测器自动选择

    # 确定专业检测选项
    enable_pavement = args.pavement or args.detection_method == 'pavement'
    enable_concrete = args.concrete or args.detection_method == 'concrete'

    # 自动选择模式
    if args.detection_method == 'auto':
        # 根据文件名或其他特征自动选择
        # 这里可以添加更智能的选择逻辑
        enable_pavement = True  # 默认启用路面检测
        enable_concrete = True  # 默认启用混凝土检测

    # 创建检测器
    detector = EnhancedCrackDetector(
        scale_factor=args.scale,
        unit=args.unit,
        yolo_model_path=yolo_model_path,
        yolo_model_id=yolo_model_id,
        enable_pavement_detection=enable_pavement,
        enable_concrete_detection=enable_concrete
    )
    
    # 更新筛选参数
    detector.update_contour_filter_params(
        min_area=args.min_area,
        max_area=args.max_area,
        min_circularity=args.min_circularity,
        max_circularity=args.max_circularity
    )
    
    if args.verbose:
        detector.print_params()
    
    return detector


def list_available_models():
    """列出所有可用模型"""
    try:
        from model_manager import model_manager

        print("\n=== 可用分割模型 ===")
        models = model_manager.get_available_models()
        installed = model_manager.get_installed_models()

        for model_id, config in models.items():
            status = "✅ 已安装" if model_id in installed else "❌ 未安装"
            print(f"\n模型ID: {model_id}")
            print(f"  名称: {config['name']}")
            print(f"  描述: {config['description']}")
            print(f"  大小: {config['size_mb']} MB")
            print(f"  速度: {config['speed_ms']} ms")
            print(f"  精度: {config['accuracy']}")
            print(f"  状态: {status}")
            print(f"  推荐用途: {', '.join(config['recommended_for'])}")

        print(f"\n总计: {len(models)} 个模型，已安装: {len(installed)} 个")

        # 推荐模型
        print("\n=== 推荐模型 ===")
        print(f"速度优先: {model_manager.recommend_model('speed')}")
        print(f"精度优先: {model_manager.recommend_model('accuracy')}")
        print(f"平衡选择: {model_manager.recommend_model('balanced')}")
        print(f"裂缝专用: {model_manager.recommend_model('crack_specific')}")

    except ImportError:
        print("模型管理器不可用")


def download_model(model_id):
    """下载指定模型"""
    try:
        from model_manager import model_manager

        print(f"\n开始下载模型: {model_id}")

        if model_id not in model_manager.get_available_models():
            print(f"错误: 未知模型ID '{model_id}'")
            print("使用 --list-models 查看可用模型")
            return False

        success = model_manager.download_model(model_id)
        if success:
            print(f"模型下载成功: {model_id}")
            return True
        else:
            print(f"模型下载失败: {model_id}")
            return False

    except ImportError:
        print("模型管理器不可用")
        return False


def process_single_image(detector, image_path, args):
    """处理单张图像"""
    print(f"处理图像: {image_path}")

    # 确定检测方法
    use_pavement = args.pavement or args.detection_method == 'pavement'
    use_concrete = args.concrete or args.detection_method == 'concrete'

    if args.detection_method == 'auto':
        # 自动选择检测方法
        use_pavement = True
        use_concrete = True

    # 检测裂缝
    result = detector.process_single_image(
        image_path,
        output_dir=args.output if args.save else None,
        use_yolo=not args.no_yolo,
        use_pavement_detection=use_pavement,
        use_concrete_detection=use_concrete
    )
    
    if 'error' in result:
        print(f"  错误: {result['error']}")
        return result
    
    # 显示基本结果
    if 'area_info' in result:
        # 标准检测结果
        area_info = result['area_info']
        print(f"  裂缝数量: {area_info['contour_count']}")
        print(f"  总面积: {area_info['total_real_area']:.2f} {args.unit}")
        print(f"  处理时间: {result['processing_time']:.2f}秒")
    elif 'pavement_result' in result:
        # 路面检测结果
        pavement_info = result['pavement_result']
        print(f"  路面裂缝数量: {pavement_info['num_cracks']}")
        print(f"  长度: {pavement_info['length_actual']:.2f} {pavement_info['unit']}")
        print(f"  平均宽度: {pavement_info['width_actual']:.2f} {pavement_info['unit']}")
        print(f"  面积: {pavement_info['area_actual']:.2f} {pavement_info['unit']}²")
    elif 'concrete_result' in result:
        # 混凝土检测结果
        concrete_info = result['concrete_result']
        print(f"  混凝土裂缝数量: {concrete_info['num_cracks']}")
        print(f"  长度: {concrete_info['length_actual']:.2f} {concrete_info['unit']}")
        print(f"  平均宽度: {concrete_info['avg_width_actual']:.2f} {concrete_info['unit']}")
        print(f"  最大宽度: {concrete_info['max_width_actual']:.2f} {concrete_info['unit']}")
        print(f"  面积: {concrete_info['area_actual']:.2f} {concrete_info['unit']}²")
        print(f"  填充率: {concrete_info['fill_ratio']:.3f}")
    
    # 计算宽度（如果需要）
    if args.width and area_info['contour_count'] > 0:
        print("  计算裂缝宽度...")
        
        width_calculator = CrackWidthCalculator(
            scale_factor=args.scale,
            unit=args.unit.replace('²', '')
        )
        
        # 从轮廓创建二值掩码
        image = cv2.imread(image_path)
        mask = np.zeros(image.shape[:2], dtype=np.uint8)
        cv2.fillPoly(mask, result['contours'], 255)
        
        width_result = width_calculator.calculate_crack_width(mask)
        
        if width_result['width_count'] > 0:
            stats = width_result['statistics']
            print(f"    宽度测量点数: {width_result['width_count']}")
            print(f"    平均宽度: {stats['mean_width']:.3f} {args.unit.replace('²', '')}")
            print(f"    最大宽度: {stats['max_width']:.3f} {args.unit.replace('²', '')}")
            print(f"    最小宽度: {stats['min_width']:.3f} {args.unit.replace('²', '')}")
            
            # 保存宽度可视化结果
            if args.save:
                width_vis = width_calculator.visualize_width_results(
                    image, width_result,
                    save_path=os.path.join(args.output, f"width_{os.path.basename(image_path)}")
                )
        else:
            print("    无法计算宽度")
        
        result['width_info'] = width_result
    
    # 显示结果图像
    if args.show:
        result_image = result['result_image']
        cv2.imshow('检测结果', result_image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    
    return result


def process_batch(detector, folder_path, args):
    """批量处理"""
    print(f"批量处理文件夹: {folder_path}")

    # 确定检测方法
    use_pavement = args.pavement or args.detection_method == 'pavement'
    use_concrete = args.concrete or args.detection_method == 'concrete'

    if args.detection_method == 'auto':
        use_pavement = True
        use_concrete = True

    results = detector.batch_process_images(
        folder_path,
        output_dir=args.output if args.save else None,
        use_yolo=not args.no_yolo,
        use_pavement_detection=use_pavement,
        use_concrete_detection=use_concrete,
        generate_csv=args.csv
    )
    
    return results


def main():
    """主函数"""
    args = parse_arguments()

    # 处理模型管理命令
    if args.list_models:
        list_available_models()
        return

    if args.download_model:
        success = download_model(args.download_model)
        if success:
            print(f"\n模型 {args.download_model} 下载完成，现在可以使用了")
        return

    print("=== 增强版裂缝检测系统 ===")
    print(f"输入: {args.source}")
    print(f"比例尺: 1像素 = {args.scale} {args.unit.replace('²', '')}")
    print(f"YOLO模型: {'禁用' if args.no_yolo else '启用'}")
    if args.yolo:
        print(f"模型路径: {args.yolo}")
    elif args.model_id:
        print(f"模型ID: {args.model_id}")
    print(f"宽度计算: {'启用' if args.width else '禁用'}")
    print()

    # 检查输入路径
    if not args.source:
        print("错误: 请指定输入路径 --source")
        sys.exit(1)

    if not os.path.exists(args.source):
        print(f"错误: 输入路径不存在: {args.source}")
        sys.exit(1)
    
    # 创建输出目录
    if args.save:
        os.makedirs(args.output, exist_ok=True)
    
    # 设置检测器
    try:
        detector = setup_detector(args)
    except Exception as e:
        print(f"错误: 检测器初始化失败: {e}")
        sys.exit(1)
    
    # 开始处理
    start_time = time.time()
    
    try:
        if args.batch or os.path.isdir(args.source):
            # 批量处理
            results = process_batch(detector, args.source, args)
        else:
            # 单张图像处理
            result = process_single_image(detector, args.source, args)
            results = [result]
        
        # 处理完成
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n=== 处理完成 ===")
        print(f"总处理时间: {total_time:.2f}秒")
        
        # 统计结果
        successful = sum(1 for r in results if 'error' not in r)
        failed = len(results) - successful
        
        print(f"成功: {successful} 张")
        print(f"失败: {failed} 张")
        print(f"总计: {len(results)} 张")
        
        if successful > 0:
            # 计算总体统计
            total_cracks = sum(r['area_info']['contour_count'] for r in results if 'error' not in r)
            total_area = sum(r['area_info']['total_real_area'] for r in results if 'error' not in r)
            
            print(f"总检测裂缝数: {total_cracks}")
            print(f"总裂缝面积: {total_area:.2f} {args.unit}")
            
            if args.save:
                print(f"结果已保存至: {args.output}")
        
    except KeyboardInterrupt:
        print("\n用户中断处理")
        sys.exit(1)
    except Exception as e:
        print(f"错误: 处理失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
